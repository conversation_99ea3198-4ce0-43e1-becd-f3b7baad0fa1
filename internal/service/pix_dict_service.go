package service

import (
	"context"

	"jdpi-gateway/internal/client"
	"jdpi-gateway/internal/converter"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	pb "jdpi-gateway/proto"

	"go.uber.org/zap"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	extresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// PixDictService PIX Dict service interface
type PixDictService interface {
	// Legacy internal protocol methods
	CreatePixKey(ctx context.Context, req *pb.CreatePixKeyRequest) (*pb.CreatePixKeyResponse, error)
	UpdatePixKey(ctx context.Context, req *pb.UpdatePixKeyRequest) (*pb.UpdatePixKeyResponse, error)
	DeletePixKey(ctx context.Context, req *pb.DeletePixKeyRequest) (*pb.DeletePixKeyResponse, error)
	NotifyAccountClosure(ctx context.Context, req *pb.NotifyAccountClosureRequest) (*pb.NotifyAccountClosureResponse, error)
	NotifyOwnershipLinkClosure(ctx context.Context, req *pb.NotifyOwnershipLinkClosureRequest) (*pb.NotifyOwnershipLinkClosureResponse, error)
	ListPixKeys(ctx context.Context, req *pb.ListPixKeysRequest) (*pb.ListPixKeysResponse, error)
	QueryPixKey(ctx context.Context, req *pb.QueryPixKeyRequest) (*pb.QueryPixKeyResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *pb.VerifyPixKeyExistenceRequest) (*pb.VerifyPixKeyExistenceResponse, error)

	// External protocol methods
	PixKeyCreate(ctx context.Context, req *extrequest.PixKeyCreateRequest) (*extresponse.PixKeyCreateResponse, error)
	PixKeyDelete(ctx context.Context, req *extrequest.PixKeyDeleteRequest) (*extresponse.PixKeyDeleteResponse, error)
	PixKeyUpdate(ctx context.Context, req *extrequest.PixKeyUpdateRequest) (*extresponse.PixKeyUpdateResponse, error)
	PixKeyListByAccount(ctx context.Context, req *extrequest.PixKeyListByAccountRequest) (*extresponse.PixKeyListByAccountResponse, error)
	PixKeyIsExist(ctx context.Context, req *extrequest.PixKeyIsExistRequest) (*extresponse.PixKeyIsExistResponse, error)
	PixKeyGet(ctx context.Context, req *extrequest.PixKeyGetRequest) (*extresponse.PixKeyGetResponse, error)
	PixKeyNotifyAccountClosure(ctx context.Context, req *extrequest.NotifyAccountClosureRequest) (*extresponse.NotifyAccountClosureResponse, error)
}

// JDPixDictService JD PIX Dict service implementation
type JDPixDictService struct {
	jdpiClient  client.JDPIClientInterface
	authService AuthService
	converter   *converter.ProtocolConverter
	logger      *zap.Logger
}

// NewPixDictService creates a new PIX Dict service
func NewPixDictService(jdpiClient client.JDPIClientInterface, authService AuthService, logger *zap.Logger) *JDPixDictService {
	return &JDPixDictService{
		jdpiClient:  jdpiClient,
		authService: authService,
		converter:   converter.NewProtocolConverter(),
		logger:      logger,
	}
}

// CreatePixKey creates a PIX key
func (s *JDPixDictService) CreatePixKey(ctx context.Context, req *pb.CreatePixKeyRequest) (*pb.CreatePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "CreatePixKey"),
	)

	logger.Info("Starting CreatePixKey operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.CreatePixKeyJDRequest{
		PixKeyType:             int(req.PixKeyType),
		PixKey:                 req.PixKey,
		Ispb:                   int(req.Ispb),
		Branch:                 req.Branch,
		AccountType:            int(req.AccountType),
		Account:                req.Account,
		AccountOpeningDatetime: req.AccountOpeningDatetime,
		PersonType:             int(req.PersonType),
		Document:               req.Document,
		Name:                   req.Name,
		TradeName:              req.TradeName,
		Reason:                 int(req.Reason),
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.CreatePixKey(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to create PIX key", zap.Error(err))
		return &pb.CreatePixKeyResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to create PIX key",
				Details: err.Error(),
			},
		}, nil
	}

	logger.Info("Successfully created PIX key")

	return &pb.CreatePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.CreatePixKeyData{
			PixKey:                       jdResponse.PixKey,
			PixKeyCreationDatetime:       jdResponse.PixKeyCreationDatetime,
			PixKeyOwnershipStartDatetime: jdResponse.PixKeyOwnershipStartDatetime,
			ClaimOpeningDatetime:         jdResponse.ClaimOpeningDatetime,
		},
	}, nil
}

// PixKeyCreate creates a PIX key using external protocol
func (s *JDPixDictService) PixKeyCreate(ctx context.Context, req *extrequest.PixKeyCreateRequest) (*extresponse.PixKeyCreateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyCreate"),
	)

	logger.Info("Starting PixKeyCreate operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest, err := s.converter.ConvertExternalToJDRequest(req)
	if err != nil {
		logger.Error("Failed to convert request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalResponse(err), nil
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.CreatePixKey(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to create PIX key", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalResponse(err), nil
	}

	logger.Info("Successfully created PIX key")

	// Convert JD response to external protocol response
	return s.converter.ConvertJDToExternalResponse(jdResponse, req.PixKey)
}

// PixKeyDelete deletes a PIX key using external protocol
func (s *JDPixDictService) PixKeyDelete(ctx context.Context, req *extrequest.PixKeyDeleteRequest) (*extresponse.PixKeyDeleteResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyDelete"),
	)

	logger.Info("Starting PixKeyDelete operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalDeleteRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalDeleteResponse(err), nil
	}

	// Convert to internal delete request
	deleteReq := &pb.DeletePixKeyRequest{
		RequestId: req.Header.IdempotenceId,
		PixKey:    req.PixKey.KeyValue,
		Ispb:      int32(req.Ispb),
		Reason:    int32(req.Reason),
	}

	// Call internal delete method
	_, err := s.DeletePixKey(ctx, deleteReq)
	if err != nil {
		logger.Error("Failed to delete PIX key", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalDeleteResponse(err), nil
	}

	logger.Info("Successfully deleted PIX key")

	// Return success response
	return &extresponse.PixKeyDeleteResponse{
		Result: &extresponse.PixKeyDeleteResponse_Response{
			Response: &extresponse.PixKeyDeleteResponseSuccess{
				PixKey: req.PixKey,
			},
		},
	}, nil
}

// PixKeyUpdate updates a PIX key using external protocol
func (s *JDPixDictService) PixKeyUpdate(ctx context.Context, req *extrequest.PixKeyUpdateRequest) (*extresponse.PixKeyUpdateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyUpdate"),
	)

	logger.Info("Starting PixKeyUpdate operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalUpdateRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalUpdateResponse(err), nil
	}

	// Convert to internal update request
	updateReq := &pb.UpdatePixKeyRequest{
		RequestId:                 req.Header.IdempotenceId,
		PixKey:                    req.PixKey.KeyValue,
		Ispb:                      int32(req.Ispb),
		NewBranch:                 req.BankAccount.BranchCode,
		NewAccountType:            int32(req.BankAccount.AccountType),
		NewAccountNumber:          req.BankAccount.AccountNumber,
		NewAccountOpeningDatetime: req.BankAccount.AccountOpeningDatetime.AsTime().Format("2006-01-02T15:04:05"),
		NewName:                   req.BankAccountHolder.HolderName,
		NewTradeName:              req.BankAccountHolder.HolderNickname,
		UpdateReason:              int32(req.Reason),
	}

	// Call internal update method
	_, err := s.UpdatePixKey(ctx, updateReq)
	if err != nil {
		logger.Error("Failed to update PIX key", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalUpdateResponse(err), nil
	}

	logger.Info("Successfully updated PIX key")

	// Return success response
	return &extresponse.PixKeyUpdateResponse{
		Result: &extresponse.PixKeyUpdateResponse_Response{
			Response: &extresponse.PixKeyUpdateResponseSuccess{
				PixKey: req.PixKey,
			},
		},
	}, nil
}

// UpdatePixKey updates a PIX key
func (s *JDPixDictService) UpdatePixKey(ctx context.Context, req *pb.UpdatePixKeyRequest) (*pb.UpdatePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "UpdatePixKey"),
	)

	logger.Info("Starting UpdatePixKey operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.UpdatePixKeyJDRequest{
		PixKey:                    req.PixKey,
		Ispb:                      int(req.Ispb),
		NewBranch:                 req.NewBranch,
		NewAccountType:            int(req.NewAccountType),
		NewAccountNumber:          req.NewAccountNumber,
		NewAccountOpeningDatetime: req.NewAccountOpeningDatetime,
		NewName:                   req.NewName,
		NewTradeName:              req.NewTradeName,
		UpdateReason:              int(req.UpdateReason),
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.UpdatePixKey(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to update PIX key", zap.Error(err))
		return &pb.UpdatePixKeyResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to update PIX key",
				Details: err.Error(),
			},
		}, nil
	}

	logger.Info("Successfully updated PIX key")

	return &pb.UpdatePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.UpdatePixKeyData{
			PixKey:                       jdResponse.PixKey,
			PixKeyCreationDatetime:       jdResponse.PixKeyCreationDatetime,
			PixKeyOwnershipStartDatetime: jdResponse.PixKeyOwnershipStartDatetime,
			ClaimOpeningDatetime:         jdResponse.ClaimOpeningDatetime,
		},
	}, nil
}

// DeletePixKey deletes a PIX key
func (s *JDPixDictService) DeletePixKey(ctx context.Context, req *pb.DeletePixKeyRequest) (*pb.DeletePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "DeletePixKey"),
	)

	logger.Info("Starting DeletePixKey operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.DeletePixKeyJDRequest{
		PixKey: req.PixKey,
		Ispb:   int(req.Ispb),
		Reason: int(req.Reason),
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.DeletePixKey(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to delete PIX key", zap.Error(err))
		return &pb.DeletePixKeyResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to delete PIX key",
				Details: err.Error(),
			},
		}, nil
	}

	logger.Info("Successfully deleted PIX key")

	return &pb.DeletePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.DeletePixKeyData{
			PixKey:                       jdResponse.PixKey,
			PixKeyCreationDatetime:       jdResponse.PixKeyCreationDatetime,
			PixKeyOwnershipStartDatetime: jdResponse.PixKeyOwnershipStartDatetime,
			ClaimOpeningDatetime:         jdResponse.ClaimOpeningDatetime,
		},
	}, nil
}

// NotifyAccountClosure notifies account closure
func (s *JDPixDictService) NotifyAccountClosure(ctx context.Context, req *pb.NotifyAccountClosureRequest) (*pb.NotifyAccountClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "NotifyAccountClosure"),
	)

	logger.Info("Starting NotifyAccountClosure operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.NotifyAccountClosureJDRequest{
		Ispb:        int(req.Ispb),
		Branch:      req.Branch,
		AccountType: int(req.AccountType),
		Account:     req.Account,
		PersonType:  int(req.PersonType),
		Document:    req.Document,
		Reason:      int(req.Reason),
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.NotifyAccountClosure(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to notify account closure", zap.Error(err))
		return &pb.NotifyAccountClosureResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to notify account closure",
				Details: err.Error(),
			},
		}, nil
	}

	logger.Info("Successfully notified account closure")

	return &pb.NotifyAccountClosureResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.NotifyAccountClosureData{
			ProcessingDatetime: jdResponse.ProcessingDatetime,
		},
	}, nil
}

// NotifyOwnershipLinkClosure notifies ownership link closure
func (s *JDPixDictService) NotifyOwnershipLinkClosure(ctx context.Context, req *pb.NotifyOwnershipLinkClosureRequest) (*pb.NotifyOwnershipLinkClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "NotifyOwnershipLinkClosure"),
	)

	logger.Info("Starting NotifyOwnershipLinkClosure operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.NotifyOwnershipLinkClosureJDRequest{
		Ispb:       int(req.Ispb),
		PersonType: int(req.PersonType),
		Document:   req.Document,
		Reason:     int(req.Reason),
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.NotifyOwnershipLinkClosure(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to notify ownership link closure", zap.Error(err))
		return &pb.NotifyOwnershipLinkClosureResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to notify ownership link closure",
				Details: err.Error(),
			},
		}, nil
	}

	logger.Info("Successfully notified ownership link closure")

	return &pb.NotifyOwnershipLinkClosureResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.NotifyOwnershipLinkClosureData{
			ProcessingDatetime: jdResponse.ProcessingDatetime,
		},
	}, nil
}

// ListPixKeys lists PIX keys
func (s *JDPixDictService) ListPixKeys(ctx context.Context, req *pb.ListPixKeysRequest) (*pb.ListPixKeysResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "ListPixKeys"),
	)

	logger.Info("Starting ListPixKeys operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.ListPixKeysJDRequest{
		Ispb:        int(req.Ispb),
		Branch:      req.Branch,
		AccountType: int(req.AccountType),
		Account:     req.Account,
		PersonType:  int(req.PersonType),
		Document:    req.Document,
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.ListPixKeys(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to list PIX keys", zap.Error(err))
		return &pb.ListPixKeysResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to list PIX keys",
				Details: err.Error(),
			},
		}, nil
	}

	// Convert JD response to gRPC response
	pixKeys := make([]*pb.AssociatedPixKey, len(jdResponse.PixKeys))
	for i, pixKey := range jdResponse.PixKeys {
		pixKeys[i] = &pb.AssociatedPixKey{
			PixKey: pixKey.PixKey,
		}
	}

	logger.Info("Successfully listed PIX keys", zap.Int("count", len(pixKeys)))

	return &pb.ListPixKeysResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.ListPixKeysData{
			JdApiReturnDatetime: jdResponse.JDApiReturnDatetime,
			PixKeys:             pixKeys,
		},
	}, nil
}

// QueryPixKey queries a PIX key
func (s *JDPixDictService) QueryPixKey(ctx context.Context, req *pb.QueryPixKeyRequest) (*pb.QueryPixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "QueryPixKey"),
	)

	logger.Info("Starting QueryPixKey operation")

	// Convert gRPC request to JD API request
	jdRequest := &model.QueryPixKeyJDRequest{
		EndToEndId: req.EndToEndId,
		PlayerId:   req.PlayerId,
		PixKey:     req.PixKey,
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.QueryPixKey(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to query PIX key", zap.Error(err))
		return &pb.QueryPixKeyResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to query PIX key",
				Details: err.Error(),
			},
		}, nil
	}

	// Convert JD response to gRPC response
	counters := make([]*pb.Counter, len(jdResponse.Statistics.Counters))
	for i, counter := range jdResponse.Statistics.Counters {
		counters[i] = &pb.Counter{
			Type:       int32(counter.Type),
			Aggregated: int32(counter.Aggregated),
			D3:         int32(counter.D3),
			D30:        int32(counter.D30),
			M6:         int32(counter.M6),
		}
	}

	logger.Info("Successfully queried PIX key")

	return &pb.QueryPixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.QueryPixKeyData{
			PixKeyType:                   int32(jdResponse.PixKeyType),
			PixKey:                       jdResponse.PixKey,
			Ispb:                         int32(jdResponse.Ispb),
			Branch:                       jdResponse.Branch,
			AccountType:                  int32(jdResponse.AccountType),
			Account:                      jdResponse.Account,
			AccountOpeningDatetime:       jdResponse.AccountOpeningDatetime,
			PersonType:                   int32(jdResponse.PersonType),
			Document:                     jdResponse.Document,
			Name:                         jdResponse.Name,
			TradeName:                    jdResponse.TradeName,
			PixKeyCreationDatetime:       jdResponse.PixKeyCreationDatetime,
			PixKeyOwnershipStartDatetime: jdResponse.PixKeyOwnershipStartDatetime,
			ClaimOpeningDatetime:         jdResponse.ClaimOpeningDatetime,
			EndToEndId:                   jdResponse.EndToEndId,
			Statistics: &pb.Statistics{
				LastAntiFraudUpdateDatetime: jdResponse.Statistics.LastAntiFraudUpdateDatetime,
				Counters:                    counters,
			},
		},
	}, nil
}

// VerifyPixKeyExistence verifies PIX key existence
func (s *JDPixDictService) VerifyPixKeyExistence(ctx context.Context, req *pb.VerifyPixKeyExistenceRequest) (*pb.VerifyPixKeyExistenceResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", req.RequestId),
		zap.String("trace_id", traceID),
		zap.String("operation", "VerifyPixKeyExistence"),
	)

	logger.Info("Starting VerifyPixKeyExistence operation")

	// Convert gRPC request to JD API request
	pixKeysToVerify := make([]model.PixKeyToVerifyJDDto, len(req.PixKeysToVerify))
	for i, pixKey := range req.PixKeysToVerify {
		pixKeysToVerify[i] = model.PixKeyToVerifyJDDto{
			PixKey: pixKey.PixKey,
		}
	}

	jdRequest := &model.VerifyPixKeyExistenceJDRequest{
		Ispb:            int(req.Ispb),
		PixKeysToVerify: pixKeysToVerify,
	}

	// Call JD API
	jdResponse, err := s.jdpiClient.VerifyPixKeyExistence(ctx, jdRequest)
	if err != nil {
		logger.Error("Failed to verify PIX key existence", zap.Error(err))
		return &pb.VerifyPixKeyExistenceResponse{
			Status: &pb.ResponseStatus{
				Code:    1,
				Message: "Failed to verify PIX key existence",
				Details: err.Error(),
			},
		}, nil
	}

	// Convert JD response to gRPC response
	verifiedPixKeys := make([]*pb.VerifiedPixKey, len(jdResponse.VerifiedPixKeys))
	for i, pixKey := range jdResponse.VerifiedPixKeys {
		verifiedPixKeys[i] = &pb.VerifiedPixKey{
			PixKey:       pixKey.PixKey,
			ExistsInDict: pixKey.ExistsInDict,
		}
	}

	logger.Info("Successfully verified PIX key existence", zap.Int("count", len(verifiedPixKeys)))

	return &pb.VerifyPixKeyExistenceResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.VerifyPixKeyExistenceData{
			DictReturnDatetime: jdResponse.DictReturnDatetime,
			CorrelationId:      jdResponse.CorrelationId,
			VerifiedPixKeys:    verifiedPixKeys,
		},
	}, nil
}

// PixKeyListByAccount lists PIX keys by account using external protocol
func (s *JDPixDictService) PixKeyListByAccount(ctx context.Context, req *extrequest.PixKeyListByAccountRequest) (*extresponse.PixKeyListByAccountResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyListByAccount"),
	)

	logger.Info("Starting PixKeyListByAccount operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalListRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalListResponse(err), nil
	}

	// Parse document ID to int64
	documentID, err := s.converter.ParseDocumentID(req.BankAccountHolder.DocumentId)
	if err != nil {
		logger.Error("Failed to parse document ID", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalListResponse(err), nil
	}

	// Convert to internal list request
	listReq := &pb.ListPixKeysRequest{
		RequestId:   req.Header.IdempotenceId,
		Ispb:        int32(req.Ispb),
		Branch:      req.BankAccount.BranchCode,
		AccountType: int32(req.BankAccount.AccountType),
		Account:     req.BankAccount.AccountNumber,
		PersonType:  int32(req.BankAccountHolder.HolderType),
		Document:    documentID,
	}

	// Call internal list method
	listResp, err := s.ListPixKeys(ctx, listReq)
	if err != nil {
		logger.Error("Failed to list PIX keys", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalListResponse(err), nil
	}

	logger.Info("Successfully listed PIX keys")

	// Convert response
	var pixKeys []*wallet.PixKey
	if listResp.Data != nil {
		for _, key := range listResp.Data.PixKeys {
			pixKeys = append(pixKeys, &wallet.PixKey{
				KeyType:  wallet.PixKeyType_EVP, // Default type since AssociatedPixKey doesn't have type
				KeyValue: key.PixKey,
			})
		}
	}

	return &extresponse.PixKeyListByAccountResponse{
		Result: &extresponse.PixKeyListByAccountResponse_Response{
			Response: &extresponse.PixKeyListByAccountResponseSuccess{
				PixKey: pixKeys,
			},
		},
	}, nil
}

// PixKeyIsExist checks if PIX keys exist using external protocol
func (s *JDPixDictService) PixKeyIsExist(ctx context.Context, req *extrequest.PixKeyIsExistRequest) (*extresponse.PixKeyIsExistResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyIsExist"),
	)

	logger.Info("Starting PixKeyIsExist operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalIsExistRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalIsExistResponse(err), nil
	}

	// Convert to internal verify request
	var pixKeysToVerify []*pb.PixKeyToVerify
	for _, key := range req.Keys {
		pixKeysToVerify = append(pixKeysToVerify, &pb.PixKeyToVerify{
			PixKey: key.KeyValue,
		})
	}

	verifyReq := &pb.VerifyPixKeyExistenceRequest{
		RequestId:       req.Header.IdempotenceId,
		Ispb:            int32(req.Ispb),
		PixKeysToVerify: pixKeysToVerify,
	}

	// Call internal verify method
	verifyResp, err := s.VerifyPixKeyExistence(ctx, verifyReq)
	if err != nil {
		logger.Error("Failed to verify PIX key existence", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalIsExistResponse(err), nil
	}

	logger.Info("Successfully verified PIX key existence")

	// Convert response
	var existentKeys []*wallet.PixKey
	if verifyResp.Data != nil {
		for _, key := range verifyResp.Data.VerifiedPixKeys {
			if key.ExistsInDict {
				// We need to determine the key type from the original request
				// For now, we'll use a default type or try to infer it
				existentKeys = append(existentKeys, &wallet.PixKey{
					KeyType:  wallet.PixKeyType_EVP, // Default type, should be improved
					KeyValue: key.PixKey,
				})
			}
		}
	}

	return &extresponse.PixKeyIsExistResponse{
		Result: &extresponse.PixKeyIsExistResponse_Response{
			Response: &extresponse.PixKeyIsExistResponseSuccess{
				ExistentPixKey: existentKeys,
			},
		},
	}, nil
}

// PixKeyGet gets PIX key information using external protocol
func (s *JDPixDictService) PixKeyGet(ctx context.Context, req *extrequest.PixKeyGetRequest) (*extresponse.PixKeyGetResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "PixKeyGet"),
	)

	logger.Info("Starting PixKeyGet operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalGetRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalGetResponse(err), nil
	}

	// Convert to internal query request
	queryReq := &pb.QueryPixKeyRequest{
		RequestId:  req.Header.IdempotenceId,
		EndToEndId: req.E2EId,
		PlayerId:   req.RequesterDocumentId,
		PixKey:     req.PixKey.KeyValue,
	}

	// Call internal query method
	queryResp, err := s.QueryPixKey(ctx, queryReq)
	if err != nil {
		logger.Error("Failed to get PIX key", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalGetResponse(err), nil
	}

	logger.Info("Successfully got PIX key")

	// Convert response
	var pixKey *wallet.PixKey

	if queryResp.Data != nil {
		pixKey = &wallet.PixKey{
			KeyType:  wallet.PixKeyType(queryResp.Data.PixKeyType),
			KeyValue: queryResp.Data.PixKey,
		}
	}

	return &extresponse.PixKeyGetResponse{
		Result: &extresponse.PixKeyGetResponse_Response{
			Response: &extresponse.PixKeyIsExistResponseSuccess{
				ExistentPixKey: []*wallet.PixKey{pixKey},
			},
		},
	}, nil
}

// PixKeyNotifyAccountClosure notifies account closure using external protocol
func (s *JDPixDictService) PixKeyNotifyAccountClosure(ctx context.Context, req *extrequest.NotifyAccountClosureRequest) (*extresponse.NotifyAccountClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil && req.Header != nil {
		idempotenceID = req.Header.IdempotenceId
	}

	logger := s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", "NotifyAccountClosure"),
	)

	logger.Info("Starting NotifyAccountClosure operation")

	// Validate request using converter
	if err := s.converter.ValidateExternalNotifyRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalNotifyResponse(err), nil
	}

	// Convert to internal notify request
	notifyReq := &pb.NotifyAccountClosureRequest{
		RequestId:   req.Header.IdempotenceId,
		Ispb:        int32(req.Ispb),
		Branch:      req.BankAccount.BranchCode,
		AccountType: int32(req.BankAccount.AccountType),
		Account:     req.BankAccount.AccountNumber,
	}

	// Call internal notify method
	_, err := s.NotifyAccountClosure(ctx, notifyReq)
	if err != nil {
		logger.Error("Failed to notify account closure", zap.Error(err))
		return s.converter.ConvertJDErrorToExternalNotifyResponse(err), nil
	}

	logger.Info("Successfully notified account closure")

	// Return success response
	return &extresponse.NotifyAccountClosureResponse{
		Result: &extresponse.NotifyAccountClosureResponse_Response{
			Response: &extresponse.NotifyAccountClosureResponseSuccess{},
		},
	}, nil
}
