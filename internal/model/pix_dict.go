package model

// JD API Request/Response models for PIX Dict operations

// CreatePixKeyJDRequest JD API request for creating PIX key
type CreatePixKeyJDRequest struct {
	PixKeyType             int    `json:"tpChave" validate:"required"`
	PixKey                 string `json:"chave" validate:"required"`
	Ispb                   int    `json:"ispb" validate:"required"`
	Branch                 string `json:"nrAgencia" validate:"required"`
	AccountType            int    `json:"tpConta" validate:"required"`
	Account                string `json:"nrConta" validate:"required"`
	AccountOpeningDatetime string `json:"dtHrAberturaConta" validate:"required"`
	PersonType             int    `json:"tpPessoa" validate:"required"`
	Document               int64  `json:"cpfCnpj" validate:"required"`
	Name                   string `json:"nome" validate:"required"`
	TradeName              string `json:"nomeFantasia,omitempty"`
	Reason                 int    `json:"motivo" validate:"required"`
}

// CreatePixKeyJDResponse JD API response for creating PIX key
type CreatePixKeyJDResponse struct {
	PixKey                       string `json:"chave"`
	PixKeyCreationDatetime       string `json:"dtHrCriacaoChave"`
	PixKeyOwnershipStartDatetime string `json:"dtHrInicioPosseChave"`
	ClaimOpeningDatetime         string `json:"dtHrAberturaReivindicacao"`
}

// UpdatePixKeyJDRequest JD API request for updating PIX key
type UpdatePixKeyJDRequest struct {
	PixKey                    string `json:"chave" validate:"required"`
	Ispb                      int    `json:"ispb" validate:"required"`
	NewBranch                 string `json:"nrAgenciaNova" validate:"required"`
	NewAccountType            int    `json:"tpContaNova"`
	NewAccountNumber          string `json:"nrContaNova" validate:"required"`
	NewAccountOpeningDatetime string `json:"dtHrAberturaConta" validate:"required"`
	NewName                   string `json:"nomeNovo" validate:"required"`
	NewTradeName              string `json:"nomeFantasiaNovo,omitempty"`
	UpdateReason              int    `json:"motivo" validate:"required"`
}

// UpdatePixKeyJDResponse JD API response for updating PIX key
type UpdatePixKeyJDResponse struct {
	PixKey                       string `json:"chave"`
	PixKeyCreationDatetime       string `json:"dtHrCriacaoChave"`
	PixKeyOwnershipStartDatetime string `json:"dtHrInicioPosseChave"`
	ClaimOpeningDatetime         string `json:"dtHrAberturaReivindicacao"`
}

// DeletePixKeyJDRequest JD API request for deleting PIX key
type DeletePixKeyJDRequest struct {
	PixKey string `json:"chave" validate:"required"`
	Ispb   int    `json:"ispb" validate:"required"`
	Reason int    `json:"motivo" validate:"required"`
}

// DeletePixKeyJDResponse JD API response for deleting PIX key
type DeletePixKeyJDResponse struct {
	PixKey                       string `json:"chave"`
	PixKeyCreationDatetime       string `json:"dtHrCriacaoChave"`
	PixKeyOwnershipStartDatetime string `json:"dtHrInicioPosseChave"`
	ClaimOpeningDatetime         string `json:"dtHrAberturaReivindicacao"`
}

// NotifyAccountClosureJDRequest JD API request for notifying account closure
type NotifyAccountClosureJDRequest struct {
	Ispb        int    `json:"ispb" validate:"required"`
	Branch      string `json:"nrAgencia" validate:"required"`
	AccountType int    `json:"tpConta" validate:"required"`
	Account     string `json:"nrConta" validate:"required"`
	PersonType  int    `json:"tpPessoa" validate:"required"`
	Document    int64  `json:"cpfCnpj" validate:"required"`
	Reason      int    `json:"motivo" validate:"required"`
}

// NotifyAccountClosureJDResponse JD API response for notifying account closure
type NotifyAccountClosureJDResponse struct {
	ProcessingDatetime string `json:"dtHrProcessamento"`
}

// NotifyOwnershipLinkClosureJDRequest JD API request for notifying ownership link closure
type NotifyOwnershipLinkClosureJDRequest struct {
	Ispb       int   `json:"ispb" validate:"required"`
	PersonType int   `json:"tpPessoa" validate:"required"`
	Document   int64 `json:"cpfCnpj" validate:"required"`
	Reason     int   `json:"motivo" validate:"required"`
}

// NotifyOwnershipLinkClosureJDResponse JD API response for notifying ownership link closure
type NotifyOwnershipLinkClosureJDResponse struct {
	ProcessingDatetime string `json:"dtHrProcessamento"`
}

// ListPixKeysJDRequest JD API request for listing PIX keys
type ListPixKeysJDRequest struct {
	Ispb        int    `json:"ispb" validate:"required"`
	Branch      string `json:"nrAgencia" validate:"required"`
	AccountType int    `json:"tpConta" validate:"required"`
	Account     string `json:"nrConta" validate:"required"`
	PersonType  int    `json:"tpPessoa" validate:"required"`
	Document    int64  `json:"cpfCnpj" validate:"required"`
}

// ListPixKeysJDResponse JD API response for listing PIX keys
type ListPixKeysJDResponse struct {
	JDApiReturnDatetime string                  `json:"dtHrJdPi"`
	PixKeys             []AssociatedPixKeyJDDto `json:"chavesAssociadas"`
}

// AssociatedPixKeyJDDto JD API DTO for associated PIX key
type AssociatedPixKeyJDDto struct {
	PixKey string `json:"chave"`
}

// QueryPixKeyJDRequest JD API request for querying PIX key
type QueryPixKeyJDRequest struct {
	EndToEndId string `json:"endToEndId,omitempty"`
	PlayerId   string `json:"playerId,omitempty"`
	PixKey     string `json:"chave" validate:"required"`
}

// QueryPixKeyJDResponse JD API response for querying PIX key
type QueryPixKeyJDResponse struct {
	PixKeyType                   int           `json:"tpChave"`
	PixKey                       string        `json:"chave"`
	Ispb                         int           `json:"ispb"`
	Branch                       string        `json:"nrAgencia"`
	AccountType                  int           `json:"tpConta"`
	Account                      string        `json:"nrConta"`
	AccountOpeningDatetime       string        `json:"dtHrAberturaConta"`
	PersonType                   int           `json:"tpPessoa"`
	Document                     int64         `json:"cpfCnpj"`
	Name                         string        `json:"nome"`
	TradeName                    string        `json:"nomeFantasia"`
	PixKeyCreationDatetime       string        `json:"dtHrCriacaoChave"`
	PixKeyOwnershipStartDatetime string        `json:"dtHrInicioPosseChave"`
	ClaimOpeningDatetime         string        `json:"dtHrAberturaReivindicacao"`
	EndToEndId                   string        `json:"endToEndId"`
	Statistics                   StatisticsDto `json:"estatisticas"`
}

// StatisticsDto JD API DTO for statistics
type StatisticsDto struct {
	LastAntiFraudUpdateDatetime string       `json:"dtHrUltAtuAntiFraude"`
	Counters                    []CounterDto `json:"contadores"`
}

// CounterDto JD API DTO for counter
type CounterDto struct {
	Type       int `json:"tipo"`
	Aggregated int `json:"agregado"`
	D3         int `json:"d3"`
	D30        int `json:"d30"`
	M6         int `json:"m6"`
}

// VerifyPixKeyExistenceJDRequest JD API request for verifying PIX key existence
type VerifyPixKeyExistenceJDRequest struct {
	Ispb            int                   `json:"ispb" validate:"required"`
	PixKeysToVerify []PixKeyToVerifyJDDto `json:"verificarChaves" validate:"required,dive"`
}

// PixKeyToVerifyJDDto JD API DTO for PIX key existence verification
type PixKeyToVerifyJDDto struct {
	PixKey string `json:"chave" validate:"required"`
}

// VerifyPixKeyExistenceJDResponse JD API response for verifying PIX key existence
type VerifyPixKeyExistenceJDResponse struct {
	DictReturnDatetime string                `json:"dtHrRetornoDict"`
	CorrelationId      string                `json:"idCorrelacao"`
	VerifiedPixKeys    []VerifiedPixKeyJDDto `json:"chavesVerificadas"`
}

// VerifiedPixKeyJDDto JD API DTO for verified PIX key
type VerifiedPixKeyJDDto struct {
	PixKey       string `json:"chave"`
	ExistsInDict bool   `json:"existeDict"`
}

// JDPaginationHeader JD API pagination header
type JDPaginationHeader struct {
	TotalRecords    int  `json:"totalRegistros"`
	CurrentPage     int  `json:"paginaAtual"`
	PageSize        int  `json:"tamanhoPagina"`
	HasPreviousPage bool `json:"temPaginaAnterior"`
	HasNextPage     bool `json:"temProximaPagina"`
}
