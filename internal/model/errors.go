package model

import (
	"encoding/json"
	"fmt"
)

// JDAPIError represents an error from JD API with original response details
type JDAPIError struct {
	Message       string
	OriginalError string
	RawResponse   string
	StatusCode    int
	CorrelationID string
}

// Error implements the error interface
func (e *JDAPIError) Error() string {
	return e.Message
}

// GetDetailsForResponse returns the raw JD response for use in ResponseStatus.Details
func (e *JDAPIError) GetDetailsForResponse() string {
	return e.RawResponse
}

// NewJDAPIError creates a new JD API error with raw response details
func NewJDAPIError(message, originalError, rawResponse string, statusCode int, correlationID string) *JDAPIError {
	return &JDAPIError{
		Message:       message,
		OriginalError: originalError,
		RawResponse:   rawResponse,
		StatusCode:    statusCode,
		CorrelationID: correlationID,
	}
}

// IsJDAPIError checks if an error is a JD API error
func IsJDAPIError(err error) (*JDAPIError, bool) {
	if jdErr, ok := err.(*JDAPIError); ok {
		return jdErr, true
	}
	return nil, false
}

// ExtractJDErrorDetails extracts JD error details from raw response
func ExtractJDErrorDetails(rawResponse string) string {
	// Try to parse and format the JSON for better readability
	var jsonObj interface{}
	if err := json.Unmarshal([]byte(rawResponse), &jsonObj); err != nil {
		// If not valid JSON, return as is
		return rawResponse
	}

	// Re-marshal with indentation for better readability
	if formatted, err := json.MarshalIndent(jsonObj, "", "  "); err == nil {
		return string(formatted)
	}

	return rawResponse
}

// FormatJDErrorForLogging formats JD error for logging purposes
func FormatJDErrorForLogging(rawResponse string, statusCode int) string {
	return fmt.Sprintf("JD API Error (HTTP %d): %s", statusCode, rawResponse)
}
