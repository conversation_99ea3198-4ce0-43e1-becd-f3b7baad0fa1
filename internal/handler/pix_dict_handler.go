package handler

import (
	"context"
	"fmt"
	"time"

	"jdpi-gateway/internal/converter"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/service"
	pb "jdpi-gateway/proto"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	extresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// PixDictHandler PIX Dict gRPC handler
type PixDictHandler struct {
	pb.UnimplementedPixDictServiceServer
	pixDictService service.PixDictService
	converter      *converter.ProtocolConverter
	validator      *validator.Validate
	logger         *zap.Logger
}

// NewPixDictHandler creates a new PIX Dict handler
func NewPixDictHandler(pixDictService service.PixDictService, logger *zap.Logger) *PixDictHandler {
	return &PixDictHandler{
		pixDictService: pixDictService,
		converter:      converter.NewProtocolConverter(),
		validator:      validator.New(),
		logger:         logger,
	}
}

// CreatePixKey creates a PIX key
func (h *PixDictHandler) CreatePixKey(ctx context.Context, req *pb.CreatePixKeyRequest) (*pb.CreatePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateCreatePixKeyRequest(req); err != nil {
		logger.Warn("Invalid CreatePixKey request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createCreatePixKeyErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing CreatePixKey request",
		zap.String("request_id", req.RequestId),
		zap.String("pix_key", req.PixKey),
	)

	// Call service layer
	response, err := h.pixDictService.CreatePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to create PIX key",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)

		return h.createCreatePixKeyErrorResponse(codes.Internal, "Failed to create PIX key", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed CreatePixKey request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// PixKeyCreate creates a PIX key using external protocol
func (h *PixDictHandler) PixKeyCreate(ctx context.Context, req *extrequest.PixKeyCreateRequest) (*extresponse.PixKeyCreateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalRequest(req); err != nil {
		logger.Warn("Invalid PixKeyCreate request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyCreate request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("pix_key", req.PixKey.KeyValue),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyCreate(ctx, req)
	if err != nil {
		logger.Error("Failed to create PIX key",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyCreate request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// PixKeyDelete deletes a PIX key using external protocol
func (h *PixDictHandler) PixKeyDelete(ctx context.Context, req *extrequest.PixKeyDeleteRequest) (*extresponse.PixKeyDeleteResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalDeleteRequest(req); err != nil {
		logger.Warn("Invalid PixKeyDelete request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalDeleteResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyDelete request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("pix_key", req.PixKey.KeyValue),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyDelete(ctx, req)
	if err != nil {
		logger.Error("Failed to delete PIX key",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalDeleteResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyDelete request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// PixKeyUpdate updates a PIX key using external protocol
func (h *PixDictHandler) PixKeyUpdate(ctx context.Context, req *extrequest.PixKeyUpdateRequest) (*extresponse.PixKeyUpdateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalUpdateRequest(req); err != nil {
		logger.Warn("Invalid PixKeyUpdate request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalUpdateResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyUpdate request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("pix_key", req.PixKey.KeyValue),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyUpdate(ctx, req)
	if err != nil {
		logger.Error("Failed to update PIX key",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalUpdateResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyUpdate request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// UpdatePixKey updates a PIX key
func (h *PixDictHandler) UpdatePixKey(ctx context.Context, req *pb.UpdatePixKeyRequest) (*pb.UpdatePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateUpdatePixKeyRequest(req); err != nil {
		logger.Warn("Invalid UpdatePixKey request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createUpdatePixKeyErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing UpdatePixKey request",
		zap.String("request_id", req.RequestId),
		zap.String("pix_key", req.PixKey),
	)

	// Call service layer
	response, err := h.pixDictService.UpdatePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to update PIX key",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createUpdatePixKeyErrorResponse(codes.Internal, "Failed to update PIX key", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed UpdatePixKey request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// DeletePixKey deletes a PIX key
func (h *PixDictHandler) DeletePixKey(ctx context.Context, req *pb.DeletePixKeyRequest) (*pb.DeletePixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateDeletePixKeyRequest(req); err != nil {
		logger.Warn("Invalid DeletePixKey request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createDeletePixKeyErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing DeletePixKey request",
		zap.String("request_id", req.RequestId),
		zap.String("pix_key", req.PixKey),
	)

	// Call service layer
	response, err := h.pixDictService.DeletePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to delete PIX key",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createDeletePixKeyErrorResponse(codes.Internal, "Failed to delete PIX key", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed DeletePixKey request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// NotifyAccountClosure notifies account closure
func (h *PixDictHandler) NotifyAccountClosure(ctx context.Context, req *pb.NotifyAccountClosureRequest) (*pb.NotifyAccountClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateNotifyAccountClosureRequest(req); err != nil {
		logger.Warn("Invalid NotifyAccountClosure request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createNotifyAccountClosureErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing NotifyAccountClosure request",
		zap.String("request_id", req.RequestId),
	)

	// Call service layer
	response, err := h.pixDictService.NotifyAccountClosure(ctx, req)
	if err != nil {
		logger.Error("Failed to notify account closure",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createNotifyAccountClosureErrorResponse(codes.Internal, "Failed to notify account closure", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed NotifyAccountClosure request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// NotifyOwnershipLinkClosure notifies ownership link closure
func (h *PixDictHandler) NotifyOwnershipLinkClosure(ctx context.Context, req *pb.NotifyOwnershipLinkClosureRequest) (*pb.NotifyOwnershipLinkClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateNotifyOwnershipLinkClosureRequest(req); err != nil {
		logger.Warn("Invalid NotifyOwnershipLinkClosure request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createNotifyOwnershipLinkClosureErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing NotifyOwnershipLinkClosure request",
		zap.String("request_id", req.RequestId),
	)

	// Call service layer
	response, err := h.pixDictService.NotifyOwnershipLinkClosure(ctx, req)
	if err != nil {
		logger.Error("Failed to notify ownership link closure",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createNotifyOwnershipLinkClosureErrorResponse(codes.Internal, "Failed to notify ownership link closure", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed NotifyOwnershipLinkClosure request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// ListPixKeys lists PIX keys
func (h *PixDictHandler) ListPixKeys(ctx context.Context, req *pb.ListPixKeysRequest) (*pb.ListPixKeysResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateListPixKeysRequest(req); err != nil {
		logger.Warn("Invalid ListPixKeys request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createListPixKeysErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing ListPixKeys request",
		zap.String("request_id", req.RequestId),
	)

	// Call service layer
	response, err := h.pixDictService.ListPixKeys(ctx, req)
	if err != nil {
		logger.Error("Failed to list PIX keys",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createListPixKeysErrorResponse(codes.Internal, "Failed to list PIX keys", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed ListPixKeys request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// QueryPixKey queries a PIX key
func (h *PixDictHandler) QueryPixKey(ctx context.Context, req *pb.QueryPixKeyRequest) (*pb.QueryPixKeyResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateQueryPixKeyRequest(req); err != nil {
		logger.Warn("Invalid QueryPixKey request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createQueryPixKeyErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing QueryPixKey request",
		zap.String("request_id", req.RequestId),
		zap.String("pix_key", req.PixKey),
	)

	// Call service layer
	response, err := h.pixDictService.QueryPixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to query PIX key",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createQueryPixKeyErrorResponse(codes.Internal, "Failed to query PIX key", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed QueryPixKey request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// VerifyPixKeyExistence verifies PIX key existence
func (h *PixDictHandler) VerifyPixKeyExistence(ctx context.Context, req *pb.VerifyPixKeyExistenceRequest) (*pb.VerifyPixKeyExistenceResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateVerifyPixKeyExistenceRequest(req); err != nil {
		logger.Warn("Invalid VerifyPixKeyExistence request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createVerifyPixKeyExistenceErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID if not provided
	if req.RequestId == "" {
		req.RequestId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing VerifyPixKeyExistence request",
		zap.String("request_id", req.RequestId),
		zap.Int("pix_keys_count", len(req.PixKeysToVerify)),
	)

	// Call service layer
	response, err := h.pixDictService.VerifyPixKeyExistence(ctx, req)
	if err != nil {
		logger.Error("Failed to verify PIX key existence",
			zap.String("request_id", req.RequestId),
			zap.Error(err),
		)
		return h.createVerifyPixKeyExistenceErrorResponse(codes.Internal, "Failed to verify PIX key existence", h.getErrorDetails(err)), nil
	}

	logger.Info("Successfully processed VerifyPixKeyExistence request",
		zap.String("request_id", req.RequestId),
	)

	return response, nil
}

// Validation methods

// validateCreatePixKeyRequest validates the CreatePixKey request
func (h *PixDictHandler) validateCreatePixKeyRequest(req *pb.CreatePixKeyRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.PixKey == "" {
		return fmt.Errorf("pix_key is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.Branch == "" {
		return fmt.Errorf("branch is required")
	}
	if req.Account == "" {
		return fmt.Errorf("account is required")
	}
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}
	return nil
}

// validateUpdatePixKeyRequest validates the UpdatePixKey request
func (h *PixDictHandler) validateUpdatePixKeyRequest(req *pb.UpdatePixKeyRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.PixKey == "" {
		return fmt.Errorf("pix_key is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.NewBranch == "" {
		return fmt.Errorf("new_branch is required")
	}
	if req.NewAccountNumber == "" {
		return fmt.Errorf("new_account_number is required")
	}
	if req.NewName == "" {
		return fmt.Errorf("new_name is required")
	}
	return nil
}

// validateDeletePixKeyRequest validates the DeletePixKey request
func (h *PixDictHandler) validateDeletePixKeyRequest(req *pb.DeletePixKeyRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.PixKey == "" {
		return fmt.Errorf("pix_key is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	return nil
}

// validateNotifyAccountClosureRequest validates the NotifyAccountClosure request
func (h *PixDictHandler) validateNotifyAccountClosureRequest(req *pb.NotifyAccountClosureRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.Branch == "" {
		return fmt.Errorf("branch is required")
	}
	if req.Account == "" {
		return fmt.Errorf("account is required")
	}
	return nil
}

// validateNotifyOwnershipLinkClosureRequest validates the NotifyOwnershipLinkClosure request
func (h *PixDictHandler) validateNotifyOwnershipLinkClosureRequest(req *pb.NotifyOwnershipLinkClosureRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.Document == 0 {
		return fmt.Errorf("document is required")
	}
	return nil
}

// validateListPixKeysRequest validates the ListPixKeys request
func (h *PixDictHandler) validateListPixKeysRequest(req *pb.ListPixKeysRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.Branch == "" {
		return fmt.Errorf("branch is required")
	}
	if req.Account == "" {
		return fmt.Errorf("account is required")
	}
	return nil
}

// validateQueryPixKeyRequest validates the QueryPixKey request
func (h *PixDictHandler) validateQueryPixKeyRequest(req *pb.QueryPixKeyRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.PixKey == "" {
		return fmt.Errorf("pix_key is required")
	}
	return nil
}

// validateVerifyPixKeyExistenceRequest validates the VerifyPixKeyExistence request
func (h *PixDictHandler) validateVerifyPixKeyExistenceRequest(req *pb.VerifyPixKeyExistenceRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if len(req.PixKeysToVerify) == 0 {
		return fmt.Errorf("pix_keys_to_verify is required")
	}
	for i, pixKey := range req.PixKeysToVerify {
		if pixKey.PixKey == "" {
			return fmt.Errorf("pix_key at index %d is required", i)
		}
	}
	return nil
}

// Error response methods

// getErrorDetails extracts error details, prioritizing JD API raw response
func (h *PixDictHandler) getErrorDetails(err error) string {
	if jdErr, ok := model.IsJDAPIError(err); ok {
		return jdErr.GetDetailsForResponse()
	}
	return err.Error()
}

// createCreatePixKeyErrorResponse creates an error response for CreatePixKey
func (h *PixDictHandler) createCreatePixKeyErrorResponse(code codes.Code, message, details string) *pb.CreatePixKeyResponse {
	return &pb.CreatePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createUpdatePixKeyErrorResponse creates an error response for UpdatePixKey
func (h *PixDictHandler) createUpdatePixKeyErrorResponse(code codes.Code, message, details string) *pb.UpdatePixKeyResponse {
	return &pb.UpdatePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createDeletePixKeyErrorResponse creates an error response for DeletePixKey
func (h *PixDictHandler) createDeletePixKeyErrorResponse(code codes.Code, message, details string) *pb.DeletePixKeyResponse {
	return &pb.DeletePixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createNotifyAccountClosureErrorResponse creates an error response for NotifyAccountClosure
func (h *PixDictHandler) createNotifyAccountClosureErrorResponse(code codes.Code, message, details string) *pb.NotifyAccountClosureResponse {
	return &pb.NotifyAccountClosureResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createNotifyOwnershipLinkClosureErrorResponse creates an error response for NotifyOwnershipLinkClosure
func (h *PixDictHandler) createNotifyOwnershipLinkClosureErrorResponse(code codes.Code, message, details string) *pb.NotifyOwnershipLinkClosureResponse {
	return &pb.NotifyOwnershipLinkClosureResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createListPixKeysErrorResponse creates an error response for ListPixKeys
func (h *PixDictHandler) createListPixKeysErrorResponse(code codes.Code, message, details string) *pb.ListPixKeysResponse {
	return &pb.ListPixKeysResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createQueryPixKeyErrorResponse creates an error response for QueryPixKey
func (h *PixDictHandler) createQueryPixKeyErrorResponse(code codes.Code, message, details string) *pb.QueryPixKeyResponse {
	return &pb.QueryPixKeyResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// createVerifyPixKeyExistenceErrorResponse creates an error response for VerifyPixKeyExistence
func (h *PixDictHandler) createVerifyPixKeyExistenceErrorResponse(code codes.Code, message, details string) *pb.VerifyPixKeyExistenceResponse {
	return &pb.VerifyPixKeyExistenceResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
	}
}

// PixKeyListByAccount lists PIX keys by account using external protocol
func (h *PixDictHandler) PixKeyListByAccount(ctx context.Context, req *extrequest.PixKeyListByAccountRequest) (*extresponse.PixKeyListByAccountResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalListRequest(req); err != nil {
		logger.Warn("Invalid PixKeyListByAccount request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalListResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyListByAccount request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("account", req.BankAccount.AccountNumber),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyListByAccount(ctx, req)
	if err != nil {
		logger.Error("Failed to list PIX keys",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalListResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyListByAccount request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// PixKeyIsExist checks if PIX keys exist using external protocol
func (h *PixDictHandler) PixKeyIsExist(ctx context.Context, req *extrequest.PixKeyIsExistRequest) (*extresponse.PixKeyIsExistResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalIsExistRequest(req); err != nil {
		logger.Warn("Invalid PixKeyIsExist request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalIsExistResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyIsExist request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.Int("key_count", len(req.Keys)),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyIsExist(ctx, req)
	if err != nil {
		logger.Error("Failed to check PIX key existence",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalIsExistResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyIsExist request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// PixKeyGet gets PIX key information using external protocol
func (h *PixDictHandler) PixKeyGet(ctx context.Context, req *extrequest.PixKeyGetRequest) (*extresponse.PixKeyGetResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalGetRequest(req); err != nil {
		logger.Warn("Invalid PixKeyGet request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalGetResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing PixKeyGet request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("pix_key", req.PixKey.KeyValue),
		zap.String("e2e_id", req.E2EId),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyGet(ctx, req)
	if err != nil {
		logger.Error("Failed to get PIX key",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalGetResponse(err), nil
	}

	logger.Info("Successfully processed PixKeyGet request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}

// PixKeyNotifyAccountClosure notifies account closure using external protocol
func (h *PixDictHandler) PixKeyNotifyAccountClosure(ctx context.Context, req *extrequest.NotifyAccountClosureRequest) (*extresponse.NotifyAccountClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation using converter
	if err := h.converter.ValidateExternalNotifyRequest(req); err != nil {
		logger.Warn("Invalid NotifyAccountClosure request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.converter.ConvertJDErrorToExternalNotifyResponse(err), nil
	}

	// Generate idempotence ID if not provided
	if req.Header == nil {
		req.Header = &extrequest.Header{}
	}
	if req.Header.IdempotenceId == "" {
		req.Header.IdempotenceId = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing NotifyAccountClosure request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
		zap.String("account", req.BankAccount.AccountNumber),
	)

	// Call service layer with external protocol
	response, err := h.pixDictService.PixKeyNotifyAccountClosure(ctx, req)
	if err != nil {
		logger.Error("Failed to notify account closure",
			zap.String("idempotence_id", req.Header.IdempotenceId),
			zap.Error(err),
		)

		return h.converter.ConvertJDErrorToExternalNotifyResponse(err), nil
	}

	logger.Info("Successfully processed NotifyAccountClosure request",
		zap.String("idempotence_id", req.Header.IdempotenceId),
	)

	return response, nil
}
