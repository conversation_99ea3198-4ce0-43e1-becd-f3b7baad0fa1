package client

import (
	"context"
	"encoding/json"
	"fmt"

	"jdpi-gateway/config"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// AuthClient JD authentication client interface
type AuthClient interface {
	GetAccessToken(ctx context.Context) (*model.AccessToken, error)
}

// JDAuthClient JD authentication client implementation
type JDAuthClient struct {
	jdpiClient JDPIClient
	config     *config.Config
	logger     *zap.Logger
}

// NewJDAuthClient creates a new JD authentication client
func NewJDAuthClient(jdpiClient JDPIClient, cfg *config.Config, logger *zap.Logger) *JDAuthClient {
	return &JDAuthClient{
		jdpiClient: jdpiClient,
		config:     cfg,
		logger:     logger,
	}
}

// GetAccessToken retrieves the access token from JD API
func (c *JDAuthClient) GetAccessToken(ctx context.Context) (*model.AccessToken, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting to get access token from JD API")

	// Use cached credentials from configuration
	clientID, clientSecret := c.config.GetEffectiveCredentials()

	if clientID == "" || clientSecret == "" {
		logger.Error("No valid credentials available")
		return nil, fmt.Errorf("no valid credentials available")
	}

	// Build request parameters
	formData := map[string]string{
		"grant_type":    c.config.JD.GrantType,
		"scope":         c.config.JD.Scope,
		"client_id":     clientID,
		"client_secret": clientSecret,
	}

	logger.Info("Sending authentication request to JD API",
		zap.String("grant_type", c.config.JD.GrantType),
		zap.String("scope", c.config.JD.Scope),
		zap.String("client_id", maskString(clientID)),
		zap.String("client_secret", maskString(clientSecret)),
		zap.String("credentials_source", c.getCredentialsSource()),
	)

	// Send request using base JD client
	resp, err := c.jdpiClient.PostForm(ctx, c.jdpiClient.GetURLs().AuthToken, formData, "")
	if err != nil {
		logger.Error("Failed to send request to JD API", zap.Error(err))
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	// Check response status
	if !resp.IsSuccess() {
		rawResponse := string(resp.Body())
		var jdError model.JDError
		if err := json.Unmarshal(resp.Body(), &jdError); err != nil {
			logger.Error("Failed to parse error response", zap.Error(err), zap.String("response", rawResponse))
			return nil, model.NewJDAPIError(
				"Authentication failed - invalid response format",
				fmt.Sprintf("HTTP %d: %s", resp.StatusCode(), rawResponse),
				rawResponse,
				resp.StatusCode(),
				"",
			)
		}
		logger.Error("JD API returned error", zap.Any("error", jdError), zap.String("detailed_error", jdError.GetDetailedErrorMessage()))
		return nil, model.NewJDAPIError(
			"Authentication failed",
			jdError.GetDetailedErrorMessage(),
			rawResponse,
			resp.StatusCode(),
			"",
		)
	}

	// Parse successful response
	var tokenResponse model.AccessTokenResponse
	if err := json.Unmarshal(resp.Body(), &tokenResponse); err != nil {
		logger.Error("Failed to parse token response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Create access token object
	accessToken := model.NewAccessToken(&tokenResponse)

	logger.Info("Successfully obtained access token",
		zap.String("token_type", accessToken.TokenType),
		zap.Int64("expires_in", accessToken.ExpiresIn),
		zap.String("scope", accessToken.Scope),
		zap.Time("expires_at", accessToken.ExpiresAt),
	)

	return accessToken, nil
}

// getCredentialsSource returns the source of credentials being used
func (c *JDAuthClient) getCredentialsSource() string {
	if c.config.JD.ClientID != "" {
		return "config"
	}
	return "environment"
}

// maskString masks sensitive string for logging
func maskString(s string) string {
	if len(s) <= 4 {
		return "****"
	}
	return s[:2] + "****" + s[len(s)-2:]
}
