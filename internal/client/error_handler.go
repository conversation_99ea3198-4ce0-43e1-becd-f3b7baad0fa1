package client

import (
	"encoding/json"
	"fmt"

	"jdpi-gateway/internal/model"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// JDErrorResponse represents the complete error response from JD API
type JDErrorResponse struct {
	JDError     *model.JDError
	RawResponse string
	StatusCode  int
}

// ParseJDError parses JD API error response and returns structured error information
func ParseJDError(resp *resty.Response, logger *zap.Logger) *JDErrorResponse {
	rawResponse := string(resp.Body())

	errorResponse := &JDErrorResponse{
		RawResponse: rawResponse,
		StatusCode:  resp.StatusCode(),
	}

	// Try to parse as JDError
	var jdError model.JDError
	if err := json.Unmarshal(resp.Body(), &jdError); err != nil {
		logger.Error("Failed to parse JD error response",
			zap.Error(err),
			zap.String("response_body", rawResponse),
			zap.Int("status_code", resp.StatusCode()),
		)
		// If parsing fails, create a generic error with the raw response
		errorResponse.JDError = &model.JDError{
			Codigo:   fmt.Sprintf("%d", resp.StatusCode()),
			Mensagem: "Failed to parse error response",
		}
	} else {
		errorResponse.JDError = &jdError
		logger.Error("JD API returned structured error",
			zap.Any("parsed_error", jdError),
			zap.String("detailed_error", jdError.GetDetailedErrorMessage()),
			zap.String("raw_response", rawResponse),
		)
	}

	return errorResponse
}

// GetErrorMessage returns the error message for logging
func (e *JDErrorResponse) GetErrorMessage() string {
	if e.JDError != nil {
		return e.JDError.Error()
	}
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.RawResponse)
}

// GetDetailedErrorMessage returns the detailed error message for response details
func (e *JDErrorResponse) GetDetailedErrorMessage() string {
	if e.JDError != nil {
		return e.JDError.GetDetailedErrorMessage()
	}
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.RawResponse)
}

// GetRawResponseForDetails returns the raw JSON response for use in ResponseStatus.Details
func (e *JDErrorResponse) GetRawResponseForDetails() string {
	return e.RawResponse
}

// Error implements the error interface
func (e *JDErrorResponse) Error() string {
	return e.GetErrorMessage()
}
