package client

import (
	"context"
	"encoding/json"
	"fmt"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"net/url"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// PixDictClient PIX Dict operations client interface
type PixDictClient interface {
	CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest) (*model.CreatePixKeyJDResponse, error)
	UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest) (*model.UpdatePixKeyJDResponse, error)
	DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest) (*model.DeletePixKeyJDResponse, error)
	NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest) (*model.NotifyAccountClosureJDResponse, error)
	NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest) (*model.NotifyOwnershipLinkClosureJDResponse, error)
	ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest) (*model.ListPixKeysJDResponse, error)
	QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest) (*model.QueryPixKeyJDResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest) (*model.VerifyPixKeyExistenceJDResponse, error)
}

// TokenProvider interface for getting access tokens
type TokenProvider interface {
	GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error)
}

// JDPixDictClient JD PIX Dict client implementation
type JDPixDictClient struct {
	jdpiClient    JDPIClient
	tokenProvider TokenProvider
	logger        *zap.Logger
}

// NewJDPixDictClient creates a new JD PIX Dict client
func NewJDPixDictClient(jdpiClient JDPIClient, tokenProvider TokenProvider, logger *zap.Logger) *JDPixDictClient {
	return &JDPixDictClient{
		jdpiClient:    jdpiClient,
		tokenProvider: tokenProvider,
		logger:        logger,
	}
}

// getAccessTokenWithAuth gets access token from token provider
func (c *JDPixDictClient) getAccessTokenWithAuth(ctx context.Context) (string, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Get access token from token provider
	token, err := c.tokenProvider.GetAccessToken(ctx, traceID)
	if err != nil {
		return "", fmt.Errorf("failed to get access token from token provider: %w", err)
	}

	if token == nil || token.AccessToken == "" {
		return "", fmt.Errorf("access token is empty")
	}

	return token.AccessToken, nil
}

// sendAuthenticatedRequest sends a request with authentication headers
func (c *JDPixDictClient) sendAuthenticatedRequest(ctx context.Context, method, url string, request interface{}, correlationId string) (*JDErrorResponse, []byte, error) {
	// Get access token
	accessToken, err := c.getAccessTokenWithAuth(ctx)
	if err != nil {
		return nil, nil, err
	}

	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("correlation_id", correlationId),
	)

	// Prepare request with authentication
	var resp *resty.Response
	switch method {
	case "POST":
		resp, err = c.jdpiClient.PostWithAuth(ctx, url, request, correlationId, accessToken)
	case "PUT":
		resp, err = c.jdpiClient.PutWithAuth(ctx, url, request, correlationId, accessToken)
	case "GET":
		resp, err = c.jdpiClient.GetWithAuth(ctx, url, correlationId, accessToken)
	default:
		return nil, nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	if err != nil {
		return nil, nil, fmt.Errorf("failed to send request: %w", err)
	}

	if !resp.IsSuccess() {
		errorResponse := ParseJDError(resp, logger)
		return errorResponse, nil, nil
	}

	return nil, resp.Body(), nil
}

// CreatePixKey creates a PIX key via JD API
func (c *JDPixDictClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest) (*model.CreatePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting CreatePixKey request to JD API")

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().CreatePixKey, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"CreatePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.CreatePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully created PIX key")
	return &response, nil
}

// UpdatePixKey updates a PIX key via JD API
func (c *JDPixDictClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest) (*model.UpdatePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting UpdatePixKey request to JD API")

	correlationId := uuid.New().String()
	url := fmt.Sprintf(c.jdpiClient.GetURLs().UpdatePixKey, req.PixKey)
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "PUT", url, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"UpdatePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.UpdatePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully updated PIX key")
	return &response, nil
}

// DeletePixKey deletes a PIX key via JD API
func (c *JDPixDictClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest) (*model.DeletePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting DeletePixKey request to JD API")

	correlationId := uuid.New().String()
	url := fmt.Sprintf(c.jdpiClient.GetURLs().DeletePixKey, req.PixKey)
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", url, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"DeletePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.DeletePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully deleted PIX key")
	return &response, nil
}

// NotifyAccountClosure notifies account closure via JD API
func (c *JDPixDictClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest) (*model.NotifyAccountClosureJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting NotifyAccountClosure request to JD API")

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().NotifyAccountClosure, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"NotifyAccountClosure failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.NotifyAccountClosureJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully notified account closure")
	return &response, nil
}

// NotifyOwnershipLinkClosure notifies ownership link closure via JD API
func (c *JDPixDictClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting NotifyOwnershipLinkClosure request to JD API")

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().NotifyOwnershipLinkClosure, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"NotifyOwnershipLinkClosure failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.NotifyOwnershipLinkClosureJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully notified ownership link closure")
	return &response, nil
}

// ListPixKeys lists PIX keys via JD API
func (c *JDPixDictClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest) (*model.ListPixKeysJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting ListPixKeys request to JD API")

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().ListPixKeys, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"ListPixKeys failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.ListPixKeysJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully listed PIX keys", zap.Int("count", len(response.PixKeys)))
	return &response, nil
}

// QueryPixKey queries a PIX key via JD API
func (c *JDPixDictClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest) (*model.QueryPixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting QueryPixKey request to JD API")

	// Build query parameters
	queryParams := url.Values{}
	if req.EndToEndId != "" {
		queryParams.Set("endToEndId", req.EndToEndId)
	}
	if req.PlayerId != "" {
		queryParams.Set("playerId", req.PlayerId)
	}

	requestURL := fmt.Sprintf(c.jdpiClient.GetURLs().QueryPixKey, req.PixKey)
	if len(queryParams) > 0 {
		requestURL += "?" + queryParams.Encode()
	}

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "GET", requestURL, nil, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"QueryPixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.QueryPixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully queried PIX key")
	return &response, nil
}

// VerifyPixKeyExistence verifies PIX key existence via JD API
func (c *JDPixDictClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest) (*model.VerifyPixKeyExistenceJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Starting VerifyPixKeyExistence request to JD API")

	correlationId := uuid.New().String()
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().VerifyPixKeyExistence, req, correlationId)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"VerifyPixKeyExistence failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			correlationId,
		)
	}

	var response model.VerifyPixKeyExistenceJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully verified PIX key existence", zap.Int("count", len(response.VerifiedPixKeys)))
	return &response, nil
}
