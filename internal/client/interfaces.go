package client

import (
	"context"

	"jdpi-gateway/internal/model"
)

// JDPIClientInterface 保持向后兼容性的统一接口
// 这个接口组合了认证和PIX Dict功能，用于现有代码的兼容性
type JDPIClientInterface interface {
	GetAccessToken(ctx context.Context) (*model.AccessToken, error)
	// PIX Dict operations
	CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest) (*model.CreatePixKeyJDResponse, error)
	UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest) (*model.UpdatePixKeyJDResponse, error)
	DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest) (*model.DeletePixKeyJDResponse, error)
	NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest) (*model.NotifyAccountClosureJDResponse, error)
	NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest) (*model.NotifyOwnershipLinkClosureJDResponse, error)
	ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest) (*model.ListPixKeysJDResponse, error)
	QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest) (*model.QueryPixKeyJDResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest) (*model.VerifyPixKeyExistenceJDResponse, error)
}

// CompositeJDPIClient 组合客户端，实现向后兼容性
type CompositeJDPIClient struct {
	authClient    AuthClient
	pixDictClient PixDictClient
}

// NewCompositeJDPIClient 创建组合客户端
func NewCompositeJDPIClient(authClient AuthClient, pixDictClient PixDictClient) *CompositeJDPIClient {
	return &CompositeJDPIClient{
		authClient:    authClient,
		pixDictClient: pixDictClient,
	}
}

// GetAccessToken 获取访问令牌
func (c *CompositeJDPIClient) GetAccessToken(ctx context.Context) (*model.AccessToken, error) {
	return c.authClient.GetAccessToken(ctx)
}

// CreatePixKey 创建PIX密钥
func (c *CompositeJDPIClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest) (*model.CreatePixKeyJDResponse, error) {
	return c.pixDictClient.CreatePixKey(ctx, req)
}

// UpdatePixKey 更新PIX密钥
func (c *CompositeJDPIClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest) (*model.UpdatePixKeyJDResponse, error) {
	return c.pixDictClient.UpdatePixKey(ctx, req)
}

// DeletePixKey 删除PIX密钥
func (c *CompositeJDPIClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest) (*model.DeletePixKeyJDResponse, error) {
	return c.pixDictClient.DeletePixKey(ctx, req)
}

// NotifyAccountClosure 通知账户关闭
func (c *CompositeJDPIClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest) (*model.NotifyAccountClosureJDResponse, error) {
	return c.pixDictClient.NotifyAccountClosure(ctx, req)
}

// NotifyOwnershipLinkClosure 通知所有权链接关闭
func (c *CompositeJDPIClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	return c.pixDictClient.NotifyOwnershipLinkClosure(ctx, req)
}

// ListPixKeys 列出PIX密钥
func (c *CompositeJDPIClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest) (*model.ListPixKeysJDResponse, error) {
	return c.pixDictClient.ListPixKeys(ctx, req)
}

// QueryPixKey 查询PIX密钥
func (c *CompositeJDPIClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest) (*model.QueryPixKeyJDResponse, error) {
	return c.pixDictClient.QueryPixKey(ctx, req)
}

// VerifyPixKeyExistence 验证PIX密钥存在性
func (c *CompositeJDPIClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest) (*model.VerifyPixKeyExistenceJDResponse, error) {
	return c.pixDictClient.VerifyPixKeyExistence(ctx, req)
}
