package mock

import (
	"context"

	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// MockCompositeJDPIClient Mock组合客户端，实现JDPIClientInterface
type MockCompositeJDPIClient struct {
	authClient    *MockAuthClient
	pixDictClient *MockPixDictClient
	logger        *zap.Logger
}

// NewMockCompositeJDPIClient 创建Mock组合客户端
func NewMockCompositeJDPIClient(logger *zap.Logger) *MockCompositeJDPIClient {
	logger.Info("Initializing Mock Composite JDPI Client")
	return &MockCompositeJDPIClient{
		authClient:    NewMockAuthClient(logger),
		pixDictClient: NewMockPixDictClient(logger),
		logger:        logger,
	}
}

// GetAccessToken Mock获取访问令牌
func (c *MockCompositeJDPIClient) GetAccessToken(ctx context.Context) (*model.AccessToken, error) {
	return c.authClient.GetAccessToken(ctx)
}

// CreatePixKey Mock创建PIX密钥
func (c *MockCompositeJDPIClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest) (*model.CreatePixKeyJDResponse, error) {
	return c.pixDictClient.CreatePixKey(ctx, req)
}

// UpdatePixKey Mock更新PIX密钥
func (c *MockCompositeJDPIClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest) (*model.UpdatePixKeyJDResponse, error) {
	return c.pixDictClient.UpdatePixKey(ctx, req)
}

// DeletePixKey Mock删除PIX密钥
func (c *MockCompositeJDPIClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest) (*model.DeletePixKeyJDResponse, error) {
	return c.pixDictClient.DeletePixKey(ctx, req)
}

// NotifyAccountClosure Mock通知账户关闭
func (c *MockCompositeJDPIClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest) (*model.NotifyAccountClosureJDResponse, error) {
	return c.pixDictClient.NotifyAccountClosure(ctx, req)
}

// NotifyOwnershipLinkClosure Mock通知所有权链接关闭
func (c *MockCompositeJDPIClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	return c.pixDictClient.NotifyOwnershipLinkClosure(ctx, req)
}

// ListPixKeys Mock列出PIX密钥
func (c *MockCompositeJDPIClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest) (*model.ListPixKeysJDResponse, error) {
	return c.pixDictClient.ListPixKeys(ctx, req)
}

// QueryPixKey Mock查询PIX密钥
func (c *MockCompositeJDPIClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest) (*model.QueryPixKeyJDResponse, error) {
	return c.pixDictClient.QueryPixKey(ctx, req)
}

// VerifyPixKeyExistence Mock验证PIX密钥存在性
func (c *MockCompositeJDPIClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest) (*model.VerifyPixKeyExistenceJDResponse, error) {
	return c.pixDictClient.VerifyPixKeyExistence(ctx, req)
}
