package converter

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"jdpi-gateway/internal/model"

	"google.golang.org/protobuf/types/known/timestamppb"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	extresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// ProtocolConverter handles conversion between external protocol and internal JD API
type ProtocolConverter struct{}

// NewProtocolConverter creates a new protocol converter
func NewProtocolConverter() *ProtocolConverter {
	return &ProtocolConverter{}
}

// ConvertExternalToJDRequest converts external PixKeyCreateRequest to JD API request
func (c *ProtocolConverter) ConvertExternalToJDRequest(req *extrequest.PixKeyCreateRequest) (*model.CreatePixKeyJDRequest, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// Parse document ID to int64
	documentID, err := c.ParseDocumentID(req.BankAccountHolder.DocumentId)
	if err != nil {
		return nil, fmt.Errorf("failed to parse document ID: %w", err)
	}

	// Parse account opening datetime
	var accountOpeningDatetime string
	if req.BankAccount.AccountOpeningDatetime != nil {
		accountOpeningDatetime = req.BankAccount.AccountOpeningDatetime.AsTime().Format("2006-01-02T15:04:05")
	}

	return &model.CreatePixKeyJDRequest{
		PixKeyType:             int(req.PixKey.KeyType),
		PixKey:                 req.PixKey.KeyValue,
		Ispb:                   int(req.Ispb),
		Branch:                 req.BankAccount.BranchCode,
		AccountType:            int(req.BankAccount.AccountType),
		Account:                req.BankAccount.AccountNumber,
		AccountOpeningDatetime: accountOpeningDatetime,
		PersonType:             int(req.BankAccountHolder.HolderType),
		Document:               documentID,
		Name:                   req.BankAccountHolder.HolderName,
		TradeName:              req.BankAccountHolder.HolderNickname,
		Reason:                 int(req.Reason),
	}, nil
}

// ConvertJDToExternalResponse converts JD API response to external PixKeyCreateResponse
func (c *ProtocolConverter) ConvertJDToExternalResponse(jdResponse *model.CreatePixKeyJDResponse, originalPixKey *wallet.PixKey) (*extresponse.PixKeyCreateResponse, error) {
	if jdResponse == nil {
		return &extresponse.PixKeyCreateResponse{
			Result: &extresponse.PixKeyCreateResponse_Error{
				Error: &extresponse.Error{
					Error:        extresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: "JD API response is nil",
				},
			},
		}, nil
	}

	// Parse timestamps
	keyCreationTime, err := c.parseTimestamp(jdResponse.PixKeyCreationDatetime)
	if err != nil {
		return &extresponse.PixKeyCreateResponse{
			Result: &extresponse.PixKeyCreateResponse_Error{
				Error: &extresponse.Error{
					Error:        extresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: "Failed to parse key creation datetime",
					ErrorDetails: err.Error(),
				},
			},
		}, fmt.Errorf("failed to parse key creation datetime: %w", err)
	}

	keyPossessionTime, err := c.parseTimestamp(jdResponse.PixKeyOwnershipStartDatetime)
	if err != nil {
		return &extresponse.PixKeyCreateResponse{
			Result: &extresponse.PixKeyCreateResponse_Error{
				Error: &extresponse.Error{
					Error:        extresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: "Failed to parse key possession datetime",
					ErrorDetails: err.Error(),
				},
			},
		}, fmt.Errorf("failed to parse key possession datetime: %w", err)
	}

	return &extresponse.PixKeyCreateResponse{
		Result: &extresponse.PixKeyCreateResponse_Response{
			Response: &extresponse.PixKeyCreateResponseSuccess{
				PixKey:                     originalPixKey,
				KeyCreationDatetime:        keyCreationTime,
				KeyPossessionStartDatetime: keyPossessionTime,
			},
		},
	}, nil
}

// ConvertJDErrorToExternalResponse converts JD API error to external error response
func (c *ProtocolConverter) ConvertJDErrorToExternalResponse(err error) *extresponse.PixKeyCreateResponse {
	// In a real implementation, you would analyze the error to determine the appropriate result
	// For now, we'll use a generic error result
	return &extresponse.PixKeyCreateResponse{
		Result: &extresponse.PixKeyCreateResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalDeleteResponse converts JD API error to external delete error response
func (c *ProtocolConverter) ConvertJDErrorToExternalDeleteResponse(err error) *extresponse.PixKeyDeleteResponse {
	return &extresponse.PixKeyDeleteResponse{
		Result: &extresponse.PixKeyDeleteResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalUpdateResponse converts JD API error to external update error response
func (c *ProtocolConverter) ConvertJDErrorToExternalUpdateResponse(err error) *extresponse.PixKeyUpdateResponse {
	return &extresponse.PixKeyUpdateResponse{
		Result: &extresponse.PixKeyUpdateResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalListResponse converts JD API error to external list error response
func (c *ProtocolConverter) ConvertJDErrorToExternalListResponse(err error) *extresponse.PixKeyListByAccountResponse {
	return &extresponse.PixKeyListByAccountResponse{
		Result: &extresponse.PixKeyListByAccountResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalIsExistResponse converts JD API error to external is exist error response
func (c *ProtocolConverter) ConvertJDErrorToExternalIsExistResponse(err error) *extresponse.PixKeyIsExistResponse {
	return &extresponse.PixKeyIsExistResponse{
		Result: &extresponse.PixKeyIsExistResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalGetResponse converts JD API error to external get error response
func (c *ProtocolConverter) ConvertJDErrorToExternalGetResponse(err error) *extresponse.PixKeyGetResponse {
	return &extresponse.PixKeyGetResponse{
		Result: &extresponse.PixKeyGetResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// ConvertJDErrorToExternalNotifyResponse converts JD API error to external notify error response
func (c *ProtocolConverter) ConvertJDErrorToExternalNotifyResponse(err error) *extresponse.NotifyAccountClosureResponse {
	return &extresponse.NotifyAccountClosureResponse{
		Result: &extresponse.NotifyAccountClosureResponse_Error{
			Error: &extresponse.Error{
				Error:        extresponse.ErrorCode_INTERNAL_ERROR,
				ErrorMessage: "Internal error occurred",
				ErrorDetails: err.Error(),
			},
		},
	}
}

// Helper methods

// ParseDocumentID converts document ID string to int64
func (c *ProtocolConverter) ParseDocumentID(documentID string) (int64, error) {
	if documentID == "" {
		return 0, fmt.Errorf("document ID cannot be empty")
	}

	// Remove any formatting characters (dots, hyphens, slashes)
	cleanDocumentID := strings.ReplaceAll(documentID, ".", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "-", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "/", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, " ", "")

	// Convert to int64
	result, err := strconv.ParseInt(cleanDocumentID, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid document ID format: %s", documentID)
	}

	return result, nil
}

// parseTimestamp converts string timestamp to protobuf timestamp
func (c *ProtocolConverter) parseTimestamp(timeStr string) (*timestamppb.Timestamp, error) {
	if timeStr == "" {
		return timestamppb.Now(), nil
	}

	// Try different timestamp formats
	formats := []string{
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return timestamppb.New(t), nil
		}
	}

	return nil, fmt.Errorf("unable to parse timestamp: %s", timeStr)
}

// ValidateExternalRequest validates external protocol request
func (c *ProtocolConverter) ValidateExternalRequest(req *extrequest.PixKeyCreateRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}

	if req.Header == nil {
		return fmt.Errorf("header is required")
	}

	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}

	if req.PixKey == nil {
		return fmt.Errorf("pix_key is required")
	}

	if req.PixKey.KeyValue == "" {
		return fmt.Errorf("pix_key value is required")
	}

	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}

	if req.BankAccount == nil {
		return fmt.Errorf("bank_account is required")
	}

	if req.BankAccount.BranchCode == "" {
		return fmt.Errorf("branch_code is required")
	}

	if req.BankAccount.AccountNumber == "" {
		return fmt.Errorf("account_number is required")
	}

	if req.BankAccountHolder == nil {
		return fmt.Errorf("bank_account_holder is required")
	}

	if req.BankAccountHolder.HolderName == "" {
		return fmt.Errorf("holder_name is required")
	}

	if req.BankAccountHolder.DocumentId == "" {
		return fmt.Errorf("document_id is required")
	}

	// Validate document ID format
	if _, err := c.ParseDocumentID(req.BankAccountHolder.DocumentId); err != nil {
		return fmt.Errorf("invalid document_id: %w", err)
	}

	return nil
}

// ValidateExternalDeleteRequest validates external PixKeyDelete request
func (c *ProtocolConverter) ValidateExternalDeleteRequest(req *extrequest.PixKeyDeleteRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.PixKey == nil {
		return fmt.Errorf("pix_key is required")
	}
	if req.PixKey.KeyValue == "" {
		return fmt.Errorf("pix_key value is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	return nil
}

// ValidateExternalUpdateRequest validates external PixKeyUpdate request
func (c *ProtocolConverter) ValidateExternalUpdateRequest(req *extrequest.PixKeyUpdateRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.PixKey == nil {
		return fmt.Errorf("pix_key is required")
	}
	if req.PixKey.KeyValue == "" {
		return fmt.Errorf("pix_key value is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.BankAccount == nil {
		return fmt.Errorf("bank_account is required")
	}
	if req.BankAccountHolder == nil {
		return fmt.Errorf("bank_account_holder is required")
	}
	return nil
}

// ValidateExternalListRequest validates external PixKeyListByAccount request
func (c *ProtocolConverter) ValidateExternalListRequest(req *extrequest.PixKeyListByAccountRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.BankAccount == nil {
		return fmt.Errorf("bank_account is required")
	}
	if req.BankAccountHolder == nil {
		return fmt.Errorf("bank_account_holder is required")
	}
	return nil
}

// ValidateExternalIsExistRequest validates external PixKeyIsExist request
func (c *ProtocolConverter) ValidateExternalIsExistRequest(req *extrequest.PixKeyIsExistRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if len(req.Keys) == 0 {
		return fmt.Errorf("at least one key is required")
	}
	for i, key := range req.Keys {
		if key == nil {
			return fmt.Errorf("key at index %d cannot be nil", i)
		}
		if key.KeyValue == "" {
			return fmt.Errorf("key value at index %d is required", i)
		}
	}
	return nil
}

// ValidateExternalGetRequest validates external PixKeyGet request
func (c *ProtocolConverter) ValidateExternalGetRequest(req *extrequest.PixKeyGetRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.PixKey == nil {
		return fmt.Errorf("pix_key is required")
	}
	if req.PixKey.KeyValue == "" {
		return fmt.Errorf("pix_key value is required")
	}
	if req.E2EId == "" {
		return fmt.Errorf("e2e_id is required")
	}
	if req.RequesterDocumentId == "" {
		return fmt.Errorf("requester_document_id is required")
	}
	return nil
}

// ValidateExternalNotifyRequest validates external NotifyAccountClosure request
func (c *ProtocolConverter) ValidateExternalNotifyRequest(req *extrequest.NotifyAccountClosureRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.Header == nil {
		return fmt.Errorf("header is required")
	}
	if req.Header.IdempotenceId == "" {
		return fmt.Errorf("idempotence_id is required")
	}
	if req.Ispb == 0 {
		return fmt.Errorf("ispb is required")
	}
	if req.BankAccount == nil {
		return fmt.Errorf("bank_account is required")
	}
	return nil
}

// GetPixKeyTypeString returns string representation of PixKeyType
func (c *ProtocolConverter) GetPixKeyTypeString(keyType wallet.PixKeyType) string {
	switch keyType {
	case wallet.PixKeyType_CPF:
		return "CPF"
	case wallet.PixKeyType_CNPJ:
		return "CNPJ"
	case wallet.PixKeyType_EMAIL:
		return "EMAIL"
	case wallet.PixKeyType_PHONE:
		return "PHONE"
	case wallet.PixKeyType_EVP:
		return "EVP"
	default:
		return "UNKNOWN"
	}
}

// GetAccountTypeString returns string representation of BankAccountType
func (c *ProtocolConverter) GetAccountTypeString(accountType wallet.BankAccountType) string {
	switch accountType {
	case wallet.BankAccountType_CHECKING_ACCOUNT:
		return "CHECKING"
	case wallet.BankAccountType_SALARY_ACCOUNT:
		return "SALARY"
	case wallet.BankAccountType_SAVINGS_ACCOUNT:
		return "SAVINGS"
	case wallet.BankAccountType_PAYMENT_ACCOUNT:
		return "PAYMENT"
	default:
		return "UNKNOWN"
	}
}

// GetPersonTypeString returns string representation of AccountHolderTypeType
func (c *ProtocolConverter) GetPersonTypeString(personType wallet.AccountHolderTypeType) string {
	switch personType {
	case wallet.AccountHolderTypeType_NATURAL:
		return "NATURAL"
	case wallet.AccountHolderTypeType_LEGAL:
		return "LEGAL"
	default:
		return "UNKNOWN"
	}
}
