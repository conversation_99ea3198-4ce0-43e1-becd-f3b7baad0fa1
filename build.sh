#!/bin/bash

MODULE_PATH="gitlab.pagsmile.com/wallet/ew_pix_adapter_proto"
PROTO_ROOT_DIR="./proto" # Diretório raiz onde seus .proto files estão, incluindo subdiretórios
GO_OUT_DIR="gen/go" # Novo diretório para o código Go gerado

echo "Verificando dependências..."

command_exists () {
  type "$1" &> /dev/null ;
}

if ! command_exists protoc; then
  echo "Erro: O compilador 'protoc' não foi encontrado."
  echo "Por favor, instale-o seguindo as instruções em: https://grpc.io/docs/protoc-installation/"
  exit 1
fi
echo "'protoc' encontrado."

export PATH=$PATH:$(go env GOPATH)/bin

if ! command_exists protoc-gen-go; then
  echo "Plugin 'protoc-gen-go' não encontrado. Instalando..."
  go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
  if [ $? -ne 0 ]; then
    echo "Erro: Falha ao instalar 'protoc-gen-go'. Verifique sua conexão e permissões."
    exit 1
  fi
fi
echo "'protoc-gen-go' encontrado/instalado."

if ! command_exists protoc-gen-go-grpc; then
  echo "Plugin 'protoc-gen-go-grpc' não encontrado. Instalando..."
  go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
  if [ $? -ne 0 ]; then
    echo "Erro: Falha ao instalar 'protoc-gen-go-grpc'. Verifique sua conexão e permissões."
    exit 1
  fi
fi
echo "'protoc-gen-go-grpc' encontrado/instalado."

echo "Dependências verificadas."

echo "Gerando código Go a partir de todos os arquivos .proto em ${PROTO_ROOT_DIR} e seus subdiretórios..."
echo "O código gerado será salvo em ${GO_OUT_DIR}/"

# Verifica se o diretório raiz dos protos existe
if [ ! -d "$PROTO_ROOT_DIR" ]; then
    echo "Erro: Diretório raiz dos protos '${PROTO_ROOT_DIR}' não encontrado."
    echo "Certifique-se de executar este script na raiz do seu módulo (mywalletmodule)."
    exit 1
fi

mkdir -p "${GO_OUT_DIR}"

ALL_PROTO_FILES=$(find "${PROTO_ROOT_DIR}" -name "*.proto")

if [ -z "$ALL_PROTO_FILES" ]; then
    echo "Nenhum arquivo .proto encontrado em '${PROTO_ROOT_DIR}'. Nada para compilar."
    exit 0
fi

find "$PROTO_ROOT_DIR" -name "*.proto" | xargs -I {} protoc \
  --proto_path="$PROTO_ROOT_DIR" \
  --go_out="$GO_OUT_DIR" \
  --go_opt=paths=source_relative \
  --go-grpc_out="$GO_OUT_DIR" \
  --go-grpc_opt=paths=source_relative \
  {}

if [ $? -eq 0 ]; then
  echo "Código Go gerado com sucesso em ${GO_OUT_DIR}/."
else
  echo "Erro: Falha ao gerar o código Go a partir do Protobuf."
  exit 1
fi

echo "Processo de geração de código Protobuf concluído."

go mod tidy

echo "script mod tidy concluid"