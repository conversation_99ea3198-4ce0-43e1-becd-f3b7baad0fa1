package service_test

import (
	"context"
	"testing"
	"time"

	"jdpi-gateway/internal/mock"
	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/service"
	pb "jdpi-gateway/proto"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// We now use the mock client from internal/mock package

// MockAuthService for testing
type MockAuthService struct {
	mockClient *mock.MockCompositeJDPIClient
}

func NewMockAuthService(logger *zap.Logger) *MockAuthService {
	return &MockAuthService{
		mockClient: mock.NewMockCompositeJDPIClient(logger),
	}
}

func (m *MockAuthService) GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error) {
	return m.mockClient.GetAccessToken(ctx)
}

func (m *MockAuthService) StartTokenRefreshRoutine(ctx context.Context) {
	// No-op for testing
}

func (m *MockAuthService) StopTokenRefreshRoutine() {
	// No-op for testing
}

func TestPixDictService_CreatePixKey_MockMode(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := &pb.CreatePixKeyRequest{
		RequestId:              "test-request-1",
		PixKeyType:             1,
		PixKey:                 "test-pix-key",
		Ispb:                   12345,
		Branch:                 "0001",
		AccountType:            1,
		Account:                "123456",
		AccountOpeningDatetime: "2024-01-01T10:00:00Z",
		PersonType:             1,
		Document:               ********901,
		Name:                   "Test User",
		Reason:                 1,
	}

	resp, err := pixDictService.CreatePixKey(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Status.Code)
	assert.Equal(t, "Success", resp.Status.Message)
	assert.NotNil(t, resp.Data)
	assert.Equal(t, req.PixKey, resp.Data.PixKey)
}

func TestPixDictService_CreatePixKey_RealMode(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := &pb.CreatePixKeyRequest{
		RequestId:              "test-request-1",
		PixKeyType:             1,
		PixKey:                 "test-pix-key",
		Ispb:                   12345,
		Branch:                 "0001",
		AccountType:            1,
		Account:                "123456",
		AccountOpeningDatetime: "2024-01-01T10:00:00Z",
		PersonType:             1,
		Document:               ********901,
		Name:                   "Test User",
		Reason:                 1,
	}

	// Mock client will automatically return mock data

	resp, err := pixDictService.CreatePixKey(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Status.Code)
	assert.Equal(t, "Success", resp.Status.Message)
	assert.NotNil(t, resp.Data)
	assert.Equal(t, req.PixKey, resp.Data.PixKey)
	assert.Equal(t, "2024-01-01T10:00:00Z", resp.Data.PixKeyCreationDatetime)
}

func TestPixDictService_ListPixKeys_MockMode(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := &pb.ListPixKeysRequest{
		RequestId:   "test-request-1",
		Ispb:        12345,
		Branch:      "0001",
		AccountType: 1,
		Account:     "123456",
		PersonType:  1,
		Document:    ********901,
	}

	resp, err := pixDictService.ListPixKeys(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Status.Code)
	assert.Equal(t, "Success", resp.Status.Message)
	assert.NotNil(t, resp.Data)
	assert.Len(t, resp.Data.PixKeys, 2)
	assert.Equal(t, "mock-pix-key-1", resp.Data.PixKeys[0].PixKey)
	assert.Equal(t, "mock-pix-key-2", resp.Data.PixKeys[1].PixKey)
}

func TestPixDictService_VerifyPixKeyExistence_MockMode(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := &pb.VerifyPixKeyExistenceRequest{
		RequestId: "test-request-1",
		Ispb:      12345,
		PixKeysToVerify: []*pb.PixKeyToVerify{
			{PixKey: "pix-key-1"},
			{PixKey: "pix-key-2"},
		},
	}

	resp, err := pixDictService.VerifyPixKeyExistence(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Status.Code)
	assert.Equal(t, "Success", resp.Status.Message)
	assert.NotNil(t, resp.Data)
	assert.Len(t, resp.Data.VerifiedPixKeys, 2)
	assert.Equal(t, "pix-key-1", resp.Data.VerifiedPixKeys[0].PixKey)
	assert.True(t, resp.Data.VerifiedPixKeys[0].ExistsInDict)
	assert.Equal(t, "pix-key-2", resp.Data.VerifiedPixKeys[1].PixKey)
	assert.True(t, resp.Data.VerifiedPixKeys[1].ExistsInDict)
}

// Tests for external protocol methods

func TestPixDictService_PixKeyCreate_ExternalProtocol_Success(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := createValidExternalPixKeyCreateRequest()

	resp, err := pixDictService.PixKeyCreate(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Check that we got a success response
	successResp := resp.GetResponse()
	assert.NotNil(t, successResp, "Should get success response")
	assert.Nil(t, resp.GetError(), "Should not get error response")

	// Verify response fields
	assert.NotNil(t, successResp.PixKey)
	assert.Equal(t, wallet.PixKeyType_EMAIL, successResp.PixKey.KeyType)
	assert.Equal(t, "<EMAIL>", successResp.PixKey.KeyValue)
	assert.NotNil(t, successResp.KeyCreationDatetime)
	assert.NotNil(t, successResp.KeyPossessionStartDatetime)
}

func TestPixDictService_PixKeyCreate_ExternalProtocol_ValidationError(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	// Test with invalid request (missing required fields)
	req := &extrequest.PixKeyCreateRequest{
		Header: &extrequest.Header{
			IdempotenceId: "test-request-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		// Missing PixKey, BankAccount, BankAccountHolder
	}

	resp, err := pixDictService.PixKeyCreate(context.Background(), req)

	assert.NoError(t, err, "Service should not return error, but error response")
	assert.NotNil(t, resp)

	// Check that we got an error response
	errorResp := resp.GetError()
	assert.NotNil(t, errorResp, "Should get error response")
	assert.Nil(t, resp.GetResponse(), "Should not get success response")

	// Verify error details
	assert.NotEqual(t, "", errorResp.ErrorMessage)
}

func TestPixDictService_PixKeyCreate_ExternalProtocol_InvalidDocumentID(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	req := createValidExternalPixKeyCreateRequest()
	req.BankAccountHolder.DocumentId = "invalid-doc-id"

	resp, err := pixDictService.PixKeyCreate(context.Background(), req)

	assert.NoError(t, err, "Service should not return error, but error response")
	assert.NotNil(t, resp)

	// Check that we got an error response
	errorResp := resp.GetError()
	assert.NotNil(t, errorResp, "Should get error response")
	assert.Nil(t, resp.GetResponse(), "Should not get success response")
}

func TestPixDictService_PixKeyCreate_ExternalProtocol_NilRequest(t *testing.T) {
	logger := zap.NewNop()
	mockClient := mock.NewMockCompositeJDPIClient(logger)
	mockAuthService := NewMockAuthService(logger)

	pixDictService := service.NewPixDictService(mockClient, mockAuthService, logger)

	resp, err := pixDictService.PixKeyCreate(context.Background(), nil)

	assert.NoError(t, err, "Service should not return error, but error response")
	assert.NotNil(t, resp)

	// Check that we got an error response
	errorResp := resp.GetError()
	assert.NotNil(t, errorResp, "Should get error response")
	assert.Nil(t, resp.GetResponse(), "Should not get success response")
}

// Helper function to create a valid external PixKeyCreateRequest for testing
func createValidExternalPixKeyCreateRequest() *extrequest.PixKeyCreateRequest {
	return &extrequest.PixKeyCreateRequest{
		Header: &extrequest.Header{
			IdempotenceId: "test-request-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "********9",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.New(time.Now()),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderTypeType_NATURAL,
			HolderName:     "John Doe",
			HolderNickname: "Johnny",
			DocumentId:     "123.456.789-01",
		},
		Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
	}
}
