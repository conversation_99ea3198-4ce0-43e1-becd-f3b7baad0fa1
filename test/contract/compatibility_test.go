package contract

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"jdpi-gateway/internal/converter"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// TestProtocolCompatibility tests the compatibility between external protocol and our internal implementation
func TestProtocolCompatibility(t *testing.T) {
	converter := converter.NewProtocolConverter()

	t.Run("Valid PixKeyCreateRequest Conversion", func(t *testing.T) {
		// Create a valid external request
		req := createValidPixKeyCreateRequest()

		// Test validation
		err := converter.ValidateExternalRequest(req)
		assert.NoError(t, err, "Valid request should pass validation")

		// Test conversion to JD request
		jdRequest, err := converter.ConvertExternalToJDRequest(req)
		require.NoError(t, err, "Valid request should convert successfully")
		assert.NotNil(t, jdRequest, "JD request should not be nil")

		// Verify converted values
		assert.Equal(t, int(wallet.PixKeyType_EMAIL), jdRequest.PixKeyType)
		assert.Equal(t, "<EMAIL>", jdRequest.PixKey)
		assert.Equal(t, ********, jdRequest.Ispb)
		assert.Equal(t, "0001", jdRequest.Branch)
		assert.Equal(t, int(wallet.BankAccountType_CHECKING_ACCOUNT), jdRequest.AccountType)
		assert.Equal(t, "********9", jdRequest.Account)
		assert.Equal(t, int(wallet.AccountHolderTypeType_NATURAL), jdRequest.PersonType)
		assert.Equal(t, int64(********901), jdRequest.Document)
		assert.Equal(t, "John Doe", jdRequest.Name)
		assert.Equal(t, "Johnny", jdRequest.TradeName)
		assert.Equal(t, int(wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST), jdRequest.Reason)
	})

	t.Run("Invalid PixKeyCreateRequest Validation", func(t *testing.T) {
		testCases := []struct {
			name        string
			modifyReq   func(*extrequest.PixKeyCreateRequest)
			expectError bool
		}{
			{
				name: "Nil request",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					// This will be tested with nil request
				},
				expectError: true,
			},
			{
				name: "Missing PixKey",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.PixKey = nil
				},
				expectError: true,
			},
			{
				name: "Empty PixKey value",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.PixKey.KeyValue = ""
				},
				expectError: true,
			},
			{
				name: "Zero ISPB",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.Ispb = 0
				},
				expectError: true,
			},
			{
				name: "Missing BankAccount",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccount = nil
				},
				expectError: true,
			},
			{
				name: "Empty BranchCode",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccount.BranchCode = ""
				},
				expectError: true,
			},
			{
				name: "Empty AccountNumber",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccount.AccountNumber = ""
				},
				expectError: true,
			},
			{
				name: "Missing BankAccountHolder",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccountHolder = nil
				},
				expectError: true,
			},
			{
				name: "Empty HolderName",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccountHolder.HolderName = ""
				},
				expectError: true,
			},
			{
				name: "Empty DocumentId",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccountHolder.DocumentId = ""
				},
				expectError: true,
			},
			{
				name: "Invalid DocumentId format",
				modifyReq: func(req *extrequest.PixKeyCreateRequest) {
					req.BankAccountHolder.DocumentId = "invalid-doc-id"
				},
				expectError: true,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				var req *extrequest.PixKeyCreateRequest
				if tc.name != "Nil request" {
					req = createValidPixKeyCreateRequest()
					tc.modifyReq(req)
				}

				err := converter.ValidateExternalRequest(req)
				if tc.expectError {
					assert.Error(t, err, "Should return validation error")
				} else {
					assert.NoError(t, err, "Should not return validation error")
				}
			})
		}
	})

	t.Run("PixKeyCreateResponse Conversion", func(t *testing.T) {
		// Test the error response conversion
		err := assert.AnError
		errorResponse := converter.ConvertJDErrorToExternalResponse(err)
		assert.NotNil(t, errorResponse, "Error response should not be nil")
		assert.NotNil(t, errorResponse.GetError(), "Error response should contain error details")
		assert.Nil(t, errorResponse.GetResponse(), "Error response should not contain success response")
	})

	t.Run("Document ID Parsing", func(t *testing.T) {
		testCases := []struct {
			input    string
			expected int64
			hasError bool
		}{
			{"********901", ********901, false},
			{"123.456.789-01", ********901, false},
			{"123.456.789/0001-01", ********9000101, false},
			{"", 0, true},
			{"invalid", 0, true},
			{"123abc456", 0, true},
		}

		for _, tc := range testCases {
			t.Run("DocumentID: "+tc.input, func(t *testing.T) {
				req := createValidPixKeyCreateRequest()
				req.BankAccountHolder.DocumentId = tc.input

				if tc.hasError {
					err := converter.ValidateExternalRequest(req)
					assert.Error(t, err, "Should return validation error for invalid document ID")
				} else {
					err := converter.ValidateExternalRequest(req)
					assert.NoError(t, err, "Should not return validation error for valid document ID")

					jdRequest, err := converter.ConvertExternalToJDRequest(req)
					require.NoError(t, err, "Should convert successfully")
					assert.Equal(t, tc.expected, jdRequest.Document, "Document ID should be parsed correctly")
				}
			})
		}
	})
}

// TestProtocolVersionCompatibility tests that our code works with different versions of the protocol
func TestProtocolVersionCompatibility(t *testing.T) {
	t.Run("Backward Compatibility - PixKeyCreate", func(t *testing.T) {
		// Test that our code can handle older versions of the protocol
		// This would involve testing with minimal required fields
		req := &extrequest.PixKeyCreateRequest{
			Header: &extrequest.Header{
				IdempotenceId: "test-request-123",
				Channel:       extrequest.ChannelAdapter_A55_JD,
			},
			Ispb: ********,
			PixKey: &wallet.PixKey{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
			BankAccount: &wallet.BankAccount{
				Ispb:          ********,
				BranchCode:    "0001",
				AccountNumber: "********9",
				AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
			},
			BankAccountHolder: &wallet.BankAccountHolder{
				HolderType: wallet.AccountHolderTypeType_NATURAL,
				HolderName: "John Doe",
				DocumentId: "********901",
			},
			Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
		}

		converter := converter.NewProtocolConverter()
		err := converter.ValidateExternalRequest(req)
		assert.NoError(t, err, "Minimal valid request should pass validation")

		jdRequest, err := converter.ConvertExternalToJDRequest(req)
		assert.NoError(t, err, "Minimal valid request should convert successfully")
		assert.NotNil(t, jdRequest, "JD request should not be nil")
	})

	t.Run("Backward Compatibility - All Other Interfaces", func(t *testing.T) {
		converter := converter.NewProtocolConverter()

		// Test PixKeyDelete minimal request
		deleteReq := &extrequest.PixKeyDeleteRequest{
			Header: &extrequest.Header{
				IdempotenceId: "delete-123",
				Channel:       extrequest.ChannelAdapter_A55_JD,
			},
			Ispb: ********,
			PixKey: &wallet.PixKey{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
			Reason: wallet.PixKeyDeleteReason_DELETE_CLIENT_REQUEST,
		}
		err := converter.ValidateExternalDeleteRequest(deleteReq)
		assert.NoError(t, err, "Minimal delete request should pass validation")

		// Test PixKeyIsExist minimal request
		isExistReq := &extrequest.PixKeyIsExistRequest{
			Header: &extrequest.Header{
				IdempotenceId: "exist-123",
				Channel:       extrequest.ChannelAdapter_A55_JD,
			},
			Ispb: ********,
			Keys: []*wallet.PixKey{
				{
					KeyType:  wallet.PixKeyType_EMAIL,
					KeyValue: "<EMAIL>",
				},
			},
		}
		err = converter.ValidateExternalIsExistRequest(isExistReq)
		assert.NoError(t, err, "Minimal is exist request should pass validation")

		// Test PixKeyGet minimal request
		getReq := &extrequest.PixKeyGetRequest{
			Header: &extrequest.Header{
				IdempotenceId: "get-123",
				Channel:       extrequest.ChannelAdapter_A55_JD,
			},
			Ispb: ********,
			PixKey: &wallet.PixKey{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
			E2EId:               "E2E123",
			RequesterDocumentId: "********901",
		}
		err = converter.ValidateExternalGetRequest(getReq)
		assert.NoError(t, err, "Minimal get request should pass validation")
	})
}

// Helper function to create a valid PixKeyCreateRequest for testing
func createValidPixKeyCreateRequest() *extrequest.PixKeyCreateRequest {
	return &extrequest.PixKeyCreateRequest{
		Header: &extrequest.Header{
			IdempotenceId: "test-request-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "********9",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.New(time.Now()),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderTypeType_NATURAL,
			HolderName:     "John Doe",
			HolderNickname: "Johnny",
			DocumentId:     "123.456.789-01",
		},
		Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
	}
}
