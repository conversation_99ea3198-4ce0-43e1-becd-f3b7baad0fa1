package contract

import (
	"testing"
)

// TestProtocolContract runs all protocol contract tests
func TestProtocolContract(t *testing.T) {
	suite := NewContractTestSuite(t)

	// PixKeyCreate interface tests
	t.Run("PixKeyCreateRequest Contract", func(t *testing.T) {
		suite.TestPixKeyCreateRequestContract()
	})
	t.Run("PixKeyCreateResponse Contract", func(t *testing.T) {
		suite.TestPixKeyCreateResponseContract()
	})

	// PixKeyDelete interface tests
	t.Run("PixKeyDeleteRequest Contract", func(t *testing.T) {
		suite.TestPixKeyDeleteRequestContract()
	})
	t.Run("PixKeyDeleteResponse Contract", func(t *testing.T) {
		suite.TestPixKeyDeleteResponseContract()
	})

	// PixKeyUpdate interface tests
	t.Run("PixKeyUpdateRequest Contract", func(t *testing.T) {
		suite.TestPixKeyUpdateRequestContract()
	})
	t.Run("PixKeyUpdateResponse Contract", func(t *testing.T) {
		suite.TestPixKeyUpdateResponseContract()
	})

	// PixKeyListByAccount interface tests
	t.Run("PixKeyListByAccountRequest Contract", func(t *testing.T) {
		suite.TestPixKeyListByAccountRequestContract()
	})
	t.Run("PixKeyListByAccountResponse Contract", func(t *testing.T) {
		suite.TestPixKeyListByAccountResponseContract()
	})

	// PixKeyIsExist interface tests
	t.Run("PixKeyIsExistRequest Contract", func(t *testing.T) {
		suite.TestPixKeyIsExistRequestContract()
	})
	t.Run("PixKeyIsExistResponse Contract", func(t *testing.T) {
		suite.TestPixKeyIsExistResponseContract()
	})

	// PixKeyGet interface tests
	t.Run("PixKeyGetRequest Contract", func(t *testing.T) {
		suite.TestPixKeyGetRequestContract()
	})
	t.Run("PixKeyGetResponse Contract", func(t *testing.T) {
		suite.TestPixKeyGetResponseContract()
	})

	// NotifyAccountClosure interface tests
	t.Run("NotifyAccountClosureRequest Contract", func(t *testing.T) {
		suite.TestNotifyAccountClosureRequestContract()
	})
	t.Run("NotifyAccountClosureResponse Contract", func(t *testing.T) {
		suite.TestNotifyAccountClosureResponseContract()
	})

	// Common structure tests
	t.Run("PixKey Contract", func(t *testing.T) {
		suite.TestPixKeyContract()
	})
	t.Run("BankAccount Contract", func(t *testing.T) {
		suite.TestBankAccountContract()
	})
	t.Run("BankAccountHolder Contract", func(t *testing.T) {
		suite.TestBankAccountHolderContract()
	})
	t.Run("Enum Values Contract", func(t *testing.T) {
		suite.TestEnumValuesContract()
	})
}

// TestProtocolContractAll runs all contract tests in a single test
func TestProtocolContractAll(t *testing.T) {
	suite := NewContractTestSuite(t)
	suite.RunAllContractTests()
}
