package contract

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	extresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// ContractTestSuite defines the contract test suite for external protocol
type ContractTestSuite struct {
	t *testing.T
}

// NewContractTestSuite creates a new contract test suite
func NewContractTestSuite(t *testing.T) *ContractTestSuite {
	return &ContractTestSuite{t: t}
}

// TestPixKeyCreateRequestContract verifies the PixKeyCreateRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyCreateRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyCreateRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test PixKey field
	pixKeyField, found := reqType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")

	// Test BankAccount field
	bankAccountField, found := reqType.FieldByName("BankAccount")
	require.True(t, found, "BankAccount field should exist")
	assert.Equal(t, "*wallet.BankAccount", bankAccountField.Type.String(), "BankAccount should be *wallet.BankAccount type")

	// Test BankAccountHolder field
	bankAccountHolderField, found := reqType.FieldByName("BankAccountHolder")
	require.True(t, found, "BankAccountHolder field should exist")
	assert.Equal(t, "*wallet.BankAccountHolder", bankAccountHolderField.Type.String(), "BankAccountHolder should be *wallet.BankAccountHolder type")

	// Test Reason field
	reasonField, found := reqType.FieldByName("Reason")
	require.True(t, found, "Reason field should exist")
	assert.Equal(t, "wallet.PixKeyCreateReason", reasonField.Type.String(), "Reason should be wallet.PixKeyCreateReason type")
}

// TestPixKeyCreateResponseContract verifies the PixKeyCreateResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyCreateResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyCreateResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (now using oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyCreateResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyCreateResponse_Result type")

	// Test PixKeyCreateResponseSuccess structure
	successResp := &extresponse.PixKeyCreateResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// Test PixKey field in success response
	pixKeyField, found := successType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist in success response")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")

	// Test KeyCreationDatetime field in success response
	keyCreationField, found := successType.FieldByName("KeyCreationDatetime")
	require.True(t, found, "KeyCreationDatetime field should exist in success response")
	assert.Equal(t, "*timestamppb.Timestamp", keyCreationField.Type.String(), "KeyCreationDatetime should be *timestamppb.Timestamp type")

	// Test KeyPossessionStartDatetime field in success response
	keyPossessionField, found := successType.FieldByName("KeyPossessionStartDatetime")
	require.True(t, found, "KeyPossessionStartDatetime field should exist in success response")
	assert.Equal(t, "*timestamppb.Timestamp", keyPossessionField.Type.String(), "KeyPossessionStartDatetime should be *timestamppb.Timestamp type")
}

// TestPixKeyContract verifies the PixKey structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyContract() {
	t := suite.t

	// Create a sample PixKey to verify structure
	pixKey := &wallet.PixKey{}

	// Verify required fields exist and have correct types
	pixKeyType := reflect.TypeOf(pixKey).Elem()

	// Test KeyType field
	keyTypeField, found := pixKeyType.FieldByName("KeyType")
	require.True(t, found, "KeyType field should exist")
	assert.Equal(t, "wallet.PixKeyType", keyTypeField.Type.String(), "KeyType should be wallet.PixKeyType type")

	// Test KeyValue field
	keyValueField, found := pixKeyType.FieldByName("KeyValue")
	require.True(t, found, "KeyValue field should exist")
	assert.Equal(t, "string", keyValueField.Type.String(), "KeyValue should be string type")
}

// TestBankAccountContract verifies the BankAccount structure hasn't changed
func (suite *ContractTestSuite) TestBankAccountContract() {
	t := suite.t

	// Create a sample BankAccount to verify structure
	bankAccount := &wallet.BankAccount{}

	// Verify required fields exist and have correct types
	bankAccountType := reflect.TypeOf(bankAccount).Elem()

	// Test Ispb field
	ispbField, found := bankAccountType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test BranchCode field
	branchCodeField, found := bankAccountType.FieldByName("BranchCode")
	require.True(t, found, "BranchCode field should exist")
	assert.Equal(t, "string", branchCodeField.Type.String(), "BranchCode should be string type")

	// Test AccountNumber field
	accountNumberField, found := bankAccountType.FieldByName("AccountNumber")
	require.True(t, found, "AccountNumber field should exist")
	assert.Equal(t, "string", accountNumberField.Type.String(), "AccountNumber should be string type")

	// Test AccountType field
	accountTypeField, found := bankAccountType.FieldByName("AccountType")
	require.True(t, found, "AccountType field should exist")
	assert.Equal(t, "wallet.BankAccountType", accountTypeField.Type.String(), "AccountType should be wallet.BankAccountType type")

	// Test AccountOpeningDatetime field
	accountOpeningField, found := bankAccountType.FieldByName("AccountOpeningDatetime")
	require.True(t, found, "AccountOpeningDatetime field should exist")
	assert.Equal(t, "*timestamppb.Timestamp", accountOpeningField.Type.String(), "AccountOpeningDatetime should be *timestamppb.Timestamp type")
}

// TestBankAccountHolderContract verifies the BankAccountHolder structure hasn't changed
func (suite *ContractTestSuite) TestBankAccountHolderContract() {
	t := suite.t

	// Create a sample BankAccountHolder to verify structure
	holder := &wallet.BankAccountHolder{}

	// Verify required fields exist and have correct types
	holderType := reflect.TypeOf(holder).Elem()

	// Test HolderType field
	holderTypeField, found := holderType.FieldByName("HolderType")
	require.True(t, found, "HolderType field should exist")
	assert.Equal(t, "wallet.AccountHolderTypeType", holderTypeField.Type.String(), "HolderType should be wallet.AccountHolderTypeType type")

	// Test HolderName field
	holderNameField, found := holderType.FieldByName("HolderName")
	require.True(t, found, "HolderName field should exist")
	assert.Equal(t, "string", holderNameField.Type.String(), "HolderName should be string type")

	// Test HolderNickname field
	holderNicknameField, found := holderType.FieldByName("HolderNickname")
	require.True(t, found, "HolderNickname field should exist")
	assert.Equal(t, "string", holderNicknameField.Type.String(), "HolderNickname should be string type")

	// Test DocumentId field
	documentIdField, found := holderType.FieldByName("DocumentId")
	require.True(t, found, "DocumentId field should exist")
	assert.Equal(t, "string", documentIdField.Type.String(), "DocumentId should be string type")
}

// TestEnumValuesContract verifies that enum values haven't changed
func (suite *ContractTestSuite) TestEnumValuesContract() {
	t := suite.t

	// Test PixKeyType enum values
	assert.Equal(t, int32(0), int32(wallet.PixKeyType_CPF), "CPF should be 0")
	assert.Equal(t, int32(1), int32(wallet.PixKeyType_CNPJ), "CNPJ should be 1")
	assert.Equal(t, int32(2), int32(wallet.PixKeyType_EMAIL), "EMAIL should be 2")
	assert.Equal(t, int32(3), int32(wallet.PixKeyType_PHONE), "PHONE should be 3")
	assert.Equal(t, int32(4), int32(wallet.PixKeyType_EVP), "EVP should be 4")

	// Test PixKeyCreateResult enum values
	assert.Equal(t, int32(0), int32(wallet.PixKeyCreateResult_CREATE_PIX_SUCCESS), "CREATE_PIX_SUCCESS should be 0")
	assert.Equal(t, int32(1), int32(wallet.PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON), "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON should be 1")
	assert.Equal(t, int32(2), int32(wallet.PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT), "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT should be 2")

	// Test BankAccountType enum values
	assert.Equal(t, int32(0), int32(wallet.BankAccountType_CHECKING_ACCOUNT), "CHECKING_ACCOUNT should be 0")
	assert.Equal(t, int32(1), int32(wallet.BankAccountType_SALARY_ACCOUNT), "SALARY_ACCOUNT should be 1")
	assert.Equal(t, int32(2), int32(wallet.BankAccountType_SAVINGS_ACCOUNT), "SAVINGS_ACCOUNT should be 2")
	assert.Equal(t, int32(3), int32(wallet.BankAccountType_PAYMENT_ACCOUNT), "PAYMENT_ACCOUNT should be 3")

	// Test AccountHolderTypeType enum values
	assert.Equal(t, int32(0), int32(wallet.AccountHolderTypeType_NATURAL), "NATURAL should be 0")
	assert.Equal(t, int32(1), int32(wallet.AccountHolderTypeType_LEGAL), "LEGAL should be 1")

	// Test PixKeyCreateReason enum values
	assert.Equal(t, int32(0), int32(wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST), "CREATE_PIXKEY_REQUEST should be 0")
	assert.Equal(t, int32(6), int32(wallet.PixKeyCreateReason_CREATE_PIXKEY_RECONCILIATION), "CREATE_PIXKEY_RECONCILIATION should be 6")
}

// TestPixKeyDeleteRequestContract verifies the PixKeyDeleteRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyDeleteRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyDeleteRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test PixKey field
	pixKeyField, found := reqType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")

	// Test Reason field
	reasonField, found := reqType.FieldByName("Reason")
	require.True(t, found, "Reason field should exist")
	assert.Equal(t, "wallet.PixKeyDeleteReason", reasonField.Type.String(), "Reason should be wallet.PixKeyDeleteReason type")
}

// TestPixKeyDeleteResponseContract verifies the PixKeyDeleteResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyDeleteResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyDeleteResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyDeleteResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyDeleteResponse_Result type")

	// Test PixKeyDeleteResponseSuccess structure
	successResp := &extresponse.PixKeyDeleteResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// Test PixKey field in success response
	pixKeyField, found := successType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist in success response")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")
}

// TestPixKeyUpdateRequestContract verifies the PixKeyUpdateRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyUpdateRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyUpdateRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test PixKey field
	pixKeyField, found := reqType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")

	// Test BankAccount field
	bankAccountField, found := reqType.FieldByName("BankAccount")
	require.True(t, found, "BankAccount field should exist")
	assert.Equal(t, "*wallet.BankAccount", bankAccountField.Type.String(), "BankAccount should be *wallet.BankAccount type")

	// Test BankAccountHolder field
	bankAccountHolderField, found := reqType.FieldByName("BankAccountHolder")
	require.True(t, found, "BankAccountHolder field should exist")
	assert.Equal(t, "*wallet.BankAccountHolder", bankAccountHolderField.Type.String(), "BankAccountHolder should be *wallet.BankAccountHolder type")

	// Test Reason field
	reasonField, found := reqType.FieldByName("Reason")
	require.True(t, found, "Reason field should exist")
	assert.Equal(t, "wallet.PixKeyUpdateReason", reasonField.Type.String(), "Reason should be wallet.PixKeyUpdateReason type")
}

// TestPixKeyUpdateResponseContract verifies the PixKeyUpdateResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyUpdateResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyUpdateResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyUpdateResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyUpdateResponse_Result type")

	// Test PixKeyUpdateResponseSuccess structure
	successResp := &extresponse.PixKeyUpdateResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// Test PixKey field in success response
	pixKeyField, found := successType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist in success response")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")
}

// TestPixKeyListByAccountRequestContract verifies the PixKeyListByAccountRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyListByAccountRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyListByAccountRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test BankAccount field
	bankAccountField, found := reqType.FieldByName("BankAccount")
	require.True(t, found, "BankAccount field should exist")
	assert.Equal(t, "*wallet.BankAccount", bankAccountField.Type.String(), "BankAccount should be *wallet.BankAccount type")

	// Test BankAccountHolder field
	bankAccountHolderField, found := reqType.FieldByName("BankAccountHolder")
	require.True(t, found, "BankAccountHolder field should exist")
	assert.Equal(t, "*wallet.BankAccountHolder", bankAccountHolderField.Type.String(), "BankAccountHolder should be *wallet.BankAccountHolder type")
}

// TestPixKeyListByAccountResponseContract verifies the PixKeyListByAccountResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyListByAccountResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyListByAccountResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyListByAccountResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyListByAccountResponse_Result type")

	// Test PixKeyListByAccountResponseSuccess structure
	successResp := &extresponse.PixKeyListByAccountResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// Test PixKey field in success response
	pixKeyField, found := successType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist in success response")
	assert.Equal(t, "[]*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be []*wallet.PixKey type")
}

// TestPixKeyIsExistRequestContract verifies the PixKeyIsExistRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyIsExistRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyIsExistRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test Keys field
	keysField, found := reqType.FieldByName("Keys")
	require.True(t, found, "Keys field should exist")
	assert.Equal(t, "[]*wallet.PixKey", keysField.Type.String(), "Keys should be []*wallet.PixKey type")
}

// TestPixKeyIsExistResponseContract verifies the PixKeyIsExistResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyIsExistResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyIsExistResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyIsExistResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyIsExistResponse_Result type")

	// Test PixKeyIsExistResponseSuccess structure
	successResp := &extresponse.PixKeyIsExistResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// Test ExistentPixKey field in success response
	existentPixKeyField, found := successType.FieldByName("ExistentPixKey")
	require.True(t, found, "ExistentPixKey field should exist in success response")
	assert.Equal(t, "[]*wallet.PixKey", existentPixKeyField.Type.String(), "ExistentPixKey should be []*wallet.PixKey type")
}

// TestPixKeyGetRequestContract verifies the PixKeyGetRequest structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyGetRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.PixKeyGetRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test PixKey field
	pixKeyField, found := reqType.FieldByName("PixKey")
	require.True(t, found, "PixKey field should exist")
	assert.Equal(t, "*wallet.PixKey", pixKeyField.Type.String(), "PixKey should be *wallet.PixKey type")

	// Test E2EId field
	e2eIdField, found := reqType.FieldByName("E2EId")
	require.True(t, found, "E2EId field should exist")
	assert.Equal(t, "string", e2eIdField.Type.String(), "E2EId should be string type")

	// Test RequesterDocumentId field
	requesterDocumentIdField, found := reqType.FieldByName("RequesterDocumentId")
	require.True(t, found, "RequesterDocumentId field should exist")
	assert.Equal(t, "string", requesterDocumentIdField.Type.String(), "RequesterDocumentId should be string type")
}

// TestPixKeyGetResponseContract verifies the PixKeyGetResponse structure hasn't changed
func (suite *ContractTestSuite) TestPixKeyGetResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.PixKeyGetResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isPixKeyGetResponse_Result", resultField.Type.String(), "Result should be response.isPixKeyGetResponse_Result type")

	// Note: PixKeyGetResponse uses PixKeyIsExistResponseSuccess in the proto definition
	// This appears to be a proto definition issue, but we test what's actually generated
}

// TestNotifyAccountClosureRequestContract verifies the NotifyAccountClosureRequest structure hasn't changed
func (suite *ContractTestSuite) TestNotifyAccountClosureRequestContract() {
	t := suite.t

	// Create a sample request to verify structure
	req := &extrequest.NotifyAccountClosureRequest{}

	// Verify required fields exist and have correct types
	reqType := reflect.TypeOf(req).Elem()

	// Test Header field
	headerField, found := reqType.FieldByName("Header")
	require.True(t, found, "Header field should exist")
	assert.Equal(t, "*request.Header", headerField.Type.String(), "Header should be *request.Header type")

	// Test Ispb field
	ispbField, found := reqType.FieldByName("Ispb")
	require.True(t, found, "Ispb field should exist")
	assert.Equal(t, "int32", ispbField.Type.String(), "Ispb should be int32 type")

	// Test BankAccount field
	bankAccountField, found := reqType.FieldByName("BankAccount")
	require.True(t, found, "BankAccount field should exist")
	assert.Equal(t, "*wallet.BankAccount", bankAccountField.Type.String(), "BankAccount should be *wallet.BankAccount type")
}

// TestNotifyAccountClosureResponseContract verifies the NotifyAccountClosureResponse structure hasn't changed
func (suite *ContractTestSuite) TestNotifyAccountClosureResponseContract() {
	t := suite.t

	// Create a sample response to verify structure
	resp := &extresponse.NotifyAccountClosureResponse{}

	// Verify required fields exist and have correct types
	respType := reflect.TypeOf(resp).Elem()

	// Test Result field (oneof pattern)
	resultField, found := respType.FieldByName("Result")
	require.True(t, found, "Result field should exist")
	assert.Equal(t, "response.isNotifyAccountClosureResponse_Result", resultField.Type.String(), "Result should be response.isNotifyAccountClosureResponse_Result type")

	// Test NotifyAccountClosureResponseSuccess structure
	successResp := &extresponse.NotifyAccountClosureResponseSuccess{}
	successType := reflect.TypeOf(successResp).Elem()

	// NotifyAccountClosureResponseSuccess is typically empty for notification responses
	assert.NotNil(t, successType, "NotifyAccountClosureResponseSuccess should exist")
}

// RunAllContractTests runs all contract tests
func (suite *ContractTestSuite) RunAllContractTests() {
	suite.TestPixKeyCreateRequestContract()
	suite.TestPixKeyCreateResponseContract()
	suite.TestPixKeyDeleteRequestContract()
	suite.TestPixKeyDeleteResponseContract()
	suite.TestPixKeyUpdateRequestContract()
	suite.TestPixKeyUpdateResponseContract()
	suite.TestPixKeyListByAccountRequestContract()
	suite.TestPixKeyListByAccountResponseContract()
	suite.TestPixKeyIsExistRequestContract()
	suite.TestPixKeyIsExistResponseContract()
	suite.TestPixKeyGetRequestContract()
	suite.TestPixKeyGetResponseContract()
	suite.TestNotifyAccountClosureRequestContract()
	suite.TestNotifyAccountClosureResponseContract()
	suite.TestPixKeyContract()
	suite.TestBankAccountContract()
	suite.TestBankAccountHolderContract()
	suite.TestEnumValuesContract()
}
