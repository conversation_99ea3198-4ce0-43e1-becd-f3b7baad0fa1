package contract

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"jdpi-gateway/internal/converter"

	// External protocol imports
	extrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// TestAllPixDictInterfacesCompatibility tests the compatibility of all PixDict interfaces
func TestAllPixDictInterfacesCompatibility(t *testing.T) {
	converter := converter.NewProtocolConverter()

	t.Run("PixKeyDelete Interface Compatibility", func(t *testing.T) {
		// Create a valid external delete request
		req := createValidPixKeyDeleteRequest()

		// Test validation
		err := converter.ValidateExternalDeleteRequest(req)
		assert.NoError(t, err, "Valid delete request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.PixKeyDeleteRequest{}
		err = converter.ValidateExternalDeleteRequest(invalidReq)
		assert.Error(t, err, "Invalid delete request should fail validation")
	})

	t.Run("PixKeyUpdate Interface Compatibility", func(t *testing.T) {
		// Create a valid external update request
		req := createValidPixKeyUpdateRequest()

		// Test validation
		err := converter.ValidateExternalUpdateRequest(req)
		assert.NoError(t, err, "Valid update request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.PixKeyUpdateRequest{}
		err = converter.ValidateExternalUpdateRequest(invalidReq)
		assert.Error(t, err, "Invalid update request should fail validation")
	})

	t.Run("PixKeyListByAccount Interface Compatibility", func(t *testing.T) {
		// Create a valid external list request
		req := createValidPixKeyListByAccountRequest()

		// Test validation
		err := converter.ValidateExternalListRequest(req)
		assert.NoError(t, err, "Valid list request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.PixKeyListByAccountRequest{}
		err = converter.ValidateExternalListRequest(invalidReq)
		assert.Error(t, err, "Invalid list request should fail validation")
	})

	t.Run("PixKeyIsExist Interface Compatibility", func(t *testing.T) {
		// Create a valid external is exist request
		req := createValidPixKeyIsExistRequest()

		// Test validation
		err := converter.ValidateExternalIsExistRequest(req)
		assert.NoError(t, err, "Valid is exist request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.PixKeyIsExistRequest{}
		err = converter.ValidateExternalIsExistRequest(invalidReq)
		assert.Error(t, err, "Invalid is exist request should fail validation")
	})

	t.Run("PixKeyGet Interface Compatibility", func(t *testing.T) {
		// Create a valid external get request
		req := createValidPixKeyGetRequest()

		// Test validation
		err := converter.ValidateExternalGetRequest(req)
		assert.NoError(t, err, "Valid get request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.PixKeyGetRequest{}
		err = converter.ValidateExternalGetRequest(invalidReq)
		assert.Error(t, err, "Invalid get request should fail validation")
	})

	t.Run("NotifyAccountClosure Interface Compatibility", func(t *testing.T) {
		// Create a valid external notify request
		req := createValidNotifyAccountClosureRequest()

		// Test validation
		err := converter.ValidateExternalNotifyRequest(req)
		assert.NoError(t, err, "Valid notify request should pass validation")

		// Test with invalid request
		invalidReq := &extrequest.NotifyAccountClosureRequest{}
		err = converter.ValidateExternalNotifyRequest(invalidReq)
		assert.Error(t, err, "Invalid notify request should fail validation")
	})
}

// TestErrorResponseCompatibility tests that all error response methods work correctly
func TestErrorResponseCompatibility(t *testing.T) {
	converter := converter.NewProtocolConverter()
	testError := assert.AnError

	t.Run("All Error Response Methods", func(t *testing.T) {
		// Test PixKeyCreate error response
		createErrorResp := converter.ConvertJDErrorToExternalResponse(testError)
		assert.NotNil(t, createErrorResp, "Create error response should not be nil")
		assert.NotNil(t, createErrorResp.GetError(), "Create error response should contain error details")

		// Test PixKeyDelete error response
		deleteErrorResp := converter.ConvertJDErrorToExternalDeleteResponse(testError)
		assert.NotNil(t, deleteErrorResp, "Delete error response should not be nil")
		assert.NotNil(t, deleteErrorResp.GetError(), "Delete error response should contain error details")

		// Test PixKeyUpdate error response
		updateErrorResp := converter.ConvertJDErrorToExternalUpdateResponse(testError)
		assert.NotNil(t, updateErrorResp, "Update error response should not be nil")
		assert.NotNil(t, updateErrorResp.GetError(), "Update error response should contain error details")

		// Test PixKeyListByAccount error response
		listErrorResp := converter.ConvertJDErrorToExternalListResponse(testError)
		assert.NotNil(t, listErrorResp, "List error response should not be nil")
		assert.NotNil(t, listErrorResp.GetError(), "List error response should contain error details")

		// Test PixKeyIsExist error response
		isExistErrorResp := converter.ConvertJDErrorToExternalIsExistResponse(testError)
		assert.NotNil(t, isExistErrorResp, "IsExist error response should not be nil")
		assert.NotNil(t, isExistErrorResp.GetError(), "IsExist error response should contain error details")

		// Test PixKeyGet error response
		getErrorResp := converter.ConvertJDErrorToExternalGetResponse(testError)
		assert.NotNil(t, getErrorResp, "Get error response should not be nil")
		assert.NotNil(t, getErrorResp.GetError(), "Get error response should contain error details")

		// Test NotifyAccountClosure error response
		notifyErrorResp := converter.ConvertJDErrorToExternalNotifyResponse(testError)
		assert.NotNil(t, notifyErrorResp, "Notify error response should not be nil")
		assert.NotNil(t, notifyErrorResp.GetError(), "Notify error response should contain error details")
	})
}

// Helper functions to create valid requests for testing

func createValidPixKeyDeleteRequest() *extrequest.PixKeyDeleteRequest {
	return &extrequest.PixKeyDeleteRequest{
		Header: &extrequest.Header{
			IdempotenceId: "delete-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		Reason: wallet.PixKeyDeleteReason_DELETE_CLIENT_REQUEST,
	}
}

func createValidPixKeyUpdateRequest() *extrequest.PixKeyUpdateRequest {
	return &extrequest.PixKeyUpdateRequest{
		Header: &extrequest.Header{
			IdempotenceId: "update-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "*********",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.New(time.Now()),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderTypeType_NATURAL,
			HolderName:     "John Doe",
			HolderNickname: "Johnny",
			DocumentId:     "123.456.789-01",
		},
		Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
	}
}

func createValidPixKeyListByAccountRequest() *extrequest.PixKeyListByAccountRequest {
	return &extrequest.PixKeyListByAccountRequest{
		Header: &extrequest.Header{
			IdempotenceId: "list-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		BankAccount: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "*********",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType: wallet.AccountHolderTypeType_NATURAL,
			HolderName: "John Doe",
			DocumentId: "123.456.789-01",
		},
	}
}

func createValidPixKeyIsExistRequest() *extrequest.PixKeyIsExistRequest {
	return &extrequest.PixKeyIsExistRequest{
		Header: &extrequest.Header{
			IdempotenceId: "exist-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		Keys: []*wallet.PixKey{
			{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
			{
				KeyType:  wallet.PixKeyType_PHONE,
				KeyValue: "+*************",
			},
		},
	}
}

func createValidPixKeyGetRequest() *extrequest.PixKeyGetRequest {
	return &extrequest.PixKeyGetRequest{
		Header: &extrequest.Header{
			IdempotenceId: "get-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		E2EId:               "E2E*********",
		RequesterDocumentId: "*********01",
	}
}

func createValidNotifyAccountClosureRequest() *extrequest.NotifyAccountClosureRequest {
	return &extrequest.NotifyAccountClosureRequest{
		Header: &extrequest.Header{
			IdempotenceId: "notify-test-123",
			Channel:       extrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		BankAccount: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "*********",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
	}
}
