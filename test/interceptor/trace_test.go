package interceptor_test

import (
	"context"
	"regexp"
	"testing"

	"jdpi-gateway/internal/interceptor"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// TestTraceIDFormat tests that the new traceID format starts with "T" followed by UUID
func TestTraceIDFormat(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Create a mock handler
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		traceID := interceptor.GetTraceIDFromContext(ctx)

		// Verify traceID format: T{UUID}
		// UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
		uuidPattern := `^T[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`
		matched, err := regexp.MatchString(uuidPattern, traceID)

		assert.NoError(t, err, "Regex should not fail")
		assert.True(t, matched, "TraceID should match format T{UUID}, got: %s", traceID)
		assert.True(t, len(traceID) == 37, "TraceID should be 37 characters long (T + 36 UUID chars), got: %d", len(traceID))
		assert.True(t, traceID[0] == 'T', "TraceID should start with 'T', got: %c", traceID[0])

		return "test response", nil
	}

	// Create interceptor
	interceptorFunc := interceptor.TraceIDInterceptor(logger)

	// Create mock gRPC info
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}

	// Test without existing traceID (should generate new one)
	ctx := context.Background()
	_, err := interceptorFunc(ctx, "test request", info, handler)
	assert.NoError(t, err)
}

// TestTraceIDExtraction tests that existing traceID is extracted from metadata
func TestTraceIDExtraction(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Create a mock handler
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		traceID := interceptor.GetTraceIDFromContext(ctx)

		// Should use the provided traceID
		assert.Equal(t, "*********-1234-1234-1234-123456789012", traceID)

		return "test response", nil
	}

	// Create interceptor
	interceptorFunc := interceptor.TraceIDInterceptor(logger)

	// Create mock gRPC info
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}

	// Test with existing traceID in metadata
	md := metadata.Pairs("x-trace-id", "*********-1234-1234-1234-123456789012")
	ctx := metadata.NewIncomingContext(context.Background(), md)

	_, err := interceptorFunc(ctx, "test request", info, handler)
	assert.NoError(t, err)
}

// TestTraceIDGeneration tests that multiple generated traceIDs are unique
func TestTraceIDGeneration(t *testing.T) {
	logger := zaptest.NewLogger(t)

	traceIDs := make(map[string]bool)

	// Create a mock handler that collects traceIDs
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		traceID := interceptor.GetTraceIDFromContext(ctx)

		// Check if traceID is unique
		assert.False(t, traceIDs[traceID], "TraceID should be unique, but got duplicate: %s", traceID)
		traceIDs[traceID] = true

		return "test response", nil
	}

	// Create interceptor
	interceptorFunc := interceptor.TraceIDInterceptor(logger)

	// Create mock gRPC info
	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}

	// Generate multiple traceIDs and verify they are unique
	for i := 0; i < 100; i++ {
		ctx := context.Background()
		_, err := interceptorFunc(ctx, "test request", info, handler)
		assert.NoError(t, err)
	}

	// Verify we generated 100 unique traceIDs
	assert.Equal(t, 100, len(traceIDs), "Should have generated 100 unique traceIDs")
}

// TestGenerateTraceID tests the generateTraceID function directly
func TestGenerateTraceID(t *testing.T) {
	// Generate multiple traceIDs
	traceIDs := make(map[string]bool)

	for i := 0; i < 10; i++ {
		traceID := interceptor.GenerateTraceID()

		// Verify format
		uuidPattern := `^T[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`
		matched, err := regexp.MatchString(uuidPattern, traceID)

		assert.NoError(t, err, "Regex should not fail")
		assert.True(t, matched, "TraceID should match format T{UUID}, got: %s", traceID)
		assert.Equal(t, 37, len(traceID), "TraceID should be 37 characters long")
		assert.Equal(t, byte('T'), traceID[0], "TraceID should start with 'T'")

		// Verify uniqueness
		assert.False(t, traceIDs[traceID], "TraceID should be unique, but got duplicate: %s", traceID)
		traceIDs[traceID] = true
	}
}
