# Secrets Manager Migration Guide

## Overview

This document describes the migration from AWS KMS to AWS Secrets Manager with configuration file fallback for JD API credentials management.

## Changes Made

### 1. Removed KMS Dependencies

- **Removed**: `pkg/kmsutil/kms.go` - KMS client implementation
- **Removed**: `github.com/aws/aws-sdk-go-v2/service/kms` dependency
- **Removed**: `KMSConfig` from configuration structure

### 2. Added Secrets Manager Support

- **Added**: `pkg/secretsmanager/secrets_manager.go` - New secrets manager implementation
- **Added**: `github.com/aws/aws-sdk-go-v2/service/secretsmanager` dependency
- **Added**: Dual-mode configuration support

### 3. Updated Configuration Structure

**Old Configuration (config.yaml)**:
```yaml
kms:
  region: "us-east-1"
  client_id_key: "jd/client_id"
  client_secret_key: "jd/client_secret"
```

**New Configuration (config.yaml)**:
```yaml
jd:
  base_url: "https://api.jd.example.com"
  grant_type: "client_credentials"
  scope: "dict_api,qrcode_api,spi_api,auth_api"
  client_id: "fallback_client_id"        # Used if Secrets Manager fails
  client_secret: "fallback_client_secret" # Used if Secrets Manager fails
  cache_mode: false
  mock_mode: false
  timeout_seconds: 30

aws:
  region: "us-east-1"

secrets:
  secret_name: "jdpi/credentials"  # AWS Secrets Manager secret name
```

## Configuration Modes

### Mode 1: AWS Secrets Manager (Primary)

When `aws.region` and `secrets.secret_name` are configured, the application will:

1. **Try AWS Secrets Manager first**
2. **Fallback to config file** if Secrets Manager fails

**AWS Secrets Manager Secret Format**:
```json
{
  "client_id": "your_jd_client_id",
  "client_secret": "your_jd_client_secret"
}
```

### Mode 2: Configuration File Only (Fallback)

When `secrets.secret_name` is empty or AWS Secrets Manager is unavailable:

1. **Use credentials from config file** (`jd.client_id` and `jd.client_secret`)

### Mode 3: Mock Mode (Testing)

When `jd.mock_mode` is `true`:

1. **Use mock credentials** for testing
2. **No external API calls** are made

## Implementation Details

### Secrets Manager Client Interface

```go
type SecretsClient interface {
    GetJDCredentials(ctx context.Context) (*JDCredentials, error)
}
```

### Available Implementations

1. **AWSSecretsManagerClient** - AWS Secrets Manager implementation
2. **ConfigFileSecretsClient** - Configuration file implementation  
3. **DualModeSecretsClient** - Tries AWS first, falls back to config
4. **MockSecretsClient** - Mock implementation for testing

### Credential Resolution Logic

```go
func (c *DualModeSecretsClient) GetJDCredentials(ctx context.Context) (*JDCredentials, error) {
    // Try AWS Secrets Manager first if available
    if c.awsClient != nil {
        credentials, err := c.awsClient.GetJDCredentials(ctx)
        if err == nil {
            return credentials, nil
        }
        // Log warning and continue to fallback
    }
    
    // Fallback to config file
    return c.configClient.GetJDCredentials(ctx)
}
```

## Migration Steps

### For Development Environment

1. **Update configuration file**:
   ```yaml
   jd:
     client_id: "your_dev_client_id"
     client_secret: "your_dev_client_secret"
   
   aws:
     region: "us-east-1"
   
   secrets:
     secret_name: ""  # Empty to use config file only
   ```

2. **Test the application**:
   ```bash
   make run
   ```

### For Production Environment

1. **Create AWS Secrets Manager secret**:
   ```bash
   aws secretsmanager create-secret \
     --name "jdpi/credentials" \
     --description "JD API credentials for JDPI Gateway" \
     --secret-string '{"client_id":"your_prod_client_id","client_secret":"your_prod_client_secret"}' \
     --region us-east-1
   ```

2. **Update configuration file**:
   ```yaml
   jd:
     client_id: "fallback_client_id"     # Fallback credentials
     client_secret: "fallback_client_secret"
   
   aws:
     region: "us-east-1"
   
   secrets:
     secret_name: "jdpi/credentials"
   ```

3. **Ensure IAM permissions**:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "secretsmanager:GetSecretValue"
         ],
         "Resource": "arn:aws:secretsmanager:us-east-1:*:secret:jdpi/credentials-*"
       }
     ]
   }
   ```

## Environment Variables

You can override configuration using environment variables:

```bash
# AWS Configuration
export JDPI_AWS_REGION="us-east-1"

# Secrets Configuration  
export JDPI_SECRETS_SECRET_NAME="jdpi/credentials"

# JD Configuration (fallback)
export JDPI_JD_CLIENT_ID="fallback_client_id"
export JDPI_JD_CLIENT_SECRET="fallback_client_secret"

# Mock mode for testing
export JDPI_JD_MOCK_MODE="true"
```

## Logging

The new implementation provides detailed logging for credential resolution:

```
INFO  Using JD credentials from AWS Secrets Manager  client_id=abcd1234***
WARN  Failed to retrieve JD credentials from AWS Secrets Manager, falling back to config file
INFO  Using JD credentials from configuration file  client_id=fallb***
```

## Security Considerations

1. **AWS Secrets Manager** provides encryption at rest and in transit
2. **Credential masking** in logs (only first 8 characters shown)
3. **Fallback credentials** should be rotated regularly
4. **IAM permissions** should follow least privilege principle

## Testing

### Unit Tests

```bash
cd test
go test -v simple_test.go
```

### Integration Tests

```bash
# Test with mock mode
export JDPI_JD_MOCK_MODE="true"
make run

# Test with config file fallback
export JDPI_SECRETS_SECRET_NAME=""
make run

# Test with AWS Secrets Manager
export JDPI_SECRETS_SECRET_NAME="jdpi/credentials"
make run
```

## Troubleshooting

### Common Issues

1. **AWS Credentials not configured**:
   ```
   ERROR Failed to initialize secrets client: failed to load AWS config
   ```
   **Solution**: Configure AWS credentials using AWS CLI, environment variables, or IAM roles.

2. **Secret not found**:
   ```
   WARN Failed to retrieve JD credentials from AWS Secrets Manager, falling back to config file
   ```
   **Solution**: Verify the secret name and ensure it exists in the correct region.

3. **Invalid secret format**:
   ```
   ERROR Failed to parse JD credentials: invalid character 'x' looking for beginning of value
   ```
   **Solution**: Ensure the secret contains valid JSON with `client_id` and `client_secret` fields.

### Debug Mode

Enable debug logging to see detailed credential resolution:

```yaml
log:
  level: "debug"
```

## Benefits

1. **Enhanced Security**: AWS Secrets Manager provides better security than KMS for credential storage
2. **Automatic Rotation**: Supports AWS Secrets Manager automatic rotation
3. **Fallback Support**: Graceful degradation to config file if AWS is unavailable
4. **Simplified Configuration**: Cleaner configuration structure
5. **Better Logging**: Detailed logging for troubleshooting
6. **Mock Support**: Easy testing with mock credentials

## Backward Compatibility

This change is **not backward compatible** with the previous KMS implementation. You must:

1. Update configuration files
2. Migrate credentials from KMS to Secrets Manager or config file
3. Update deployment scripts and documentation

The migration provides a more robust and secure credential management system with better fallback capabilities.
