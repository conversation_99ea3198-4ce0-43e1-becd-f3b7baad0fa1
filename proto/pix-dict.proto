syntax = "proto3";

package jdpi;

option go_package = "jdpi-gateway/proto;jdpi";

// Import common types from base.proto
import "proto/base.proto";

// PIX Dict Service
service PixDictService {
  // Create PIX key
  rpc CreatePixKey(CreatePixKeyRequest) returns (CreatePixKeyResponse);
  // Update PIX key
  rpc UpdatePixKey(UpdatePixKeyRequest) returns (UpdatePixKeyResponse);
  // Delete PIX key
  rpc DeletePixKey(DeletePixKeyRequest) returns (DeletePixKeyResponse);
  // Notify account closure
  rpc NotifyAccountClosure(NotifyAccountClosureRequest) returns (NotifyAccountClosureResponse);
  // Notify ownership link closure
  rpc NotifyOwnershipLinkClosure(NotifyOwnershipLinkClosureRequest) returns (NotifyOwnershipLinkClosureResponse);
  // List PIX keys
  rpc ListPixKeys(ListPixKeysRequest) returns (ListPixKeysResponse);
  // Query PIX key
  rpc QueryPixKey(QueryPixKeyRequest) returns (QueryPixKeyResponse);
  // Verify PIX key existence
  rpc VerifyPixKeyExistence(VerifyPixKeyExistenceRequest) returns (VerifyPixKeyExistenceResponse);
}

// Create PIX Key Request
message CreatePixKeyRequest {
  string request_id = 1;                   // Request ID for tracing
  int32 pix_key_type = 2;                  // tpChave
  string pix_key = 3;                      // chave
  int32 ispb = 4;                          // ispb
  string branch = 5;                       // nrAgencia
  int32 account_type = 6;                  // tpConta
  string account = 7;                      // nrConta
  string account_opening_datetime = 8;     // dtHrAberturaConta
  int32 person_type = 9;                   // tpPessoa
  int64 document = 10;                     // cpfCnpj
  string name = 11;                        // nome
  string trade_name = 12;                  // nomeFantasia (optional)
  int32 reason = 13;                       // motivo
}

// Create PIX Key Response
message CreatePixKeyResponse {
  ResponseStatus status = 1;
  CreatePixKeyData data = 2;
}

message CreatePixKeyData {
  string pix_key = 1;                      // chave
  string pix_key_creation_datetime = 2;    // dtHrCriacaoChave
  string pix_key_ownership_start_datetime = 3; // dtHrInicioPosseChave
  string claim_opening_datetime = 4;       // dtHrAberturaReivindicacao
}

// Update PIX Key Request
message UpdatePixKeyRequest {
  string request_id = 1;                   // Request ID for tracing
  string pix_key = 2;                      // chave
  int32 ispb = 3;                          // ispb
  string new_branch = 4;                   // nrAgenciaNova
  int32 new_account_type = 5;              // tpContaNova
  string new_account_number = 6;           // nrContaNova
  string new_account_opening_datetime = 7; // dtHrAberturaConta
  string new_name = 8;                     // nomeNovo
  string new_trade_name = 9;               // nomeFantasiaNovo (optional)
  int32 update_reason = 10;                // motivo
}

// Update PIX Key Response
message UpdatePixKeyResponse {
  ResponseStatus status = 1;
  UpdatePixKeyData data = 2;
}

message UpdatePixKeyData {
  string pix_key = 1;                      // chave
  string pix_key_creation_datetime = 2;    // dtHrCriacaoChave
  string pix_key_ownership_start_datetime = 3; // dtHrInicioPosseChave
  string claim_opening_datetime = 4;       // dtHrAberturaReivindicacao
}

// Delete PIX Key Request
message DeletePixKeyRequest {
  string request_id = 1;                   // Request ID for tracing
  string pix_key = 2;                      // chave
  int32 ispb = 3;                          // ispb
  int32 reason = 4;                        // motivo
}

// Delete PIX Key Response
message DeletePixKeyResponse {
  ResponseStatus status = 1;
  DeletePixKeyData data = 2;
}

message DeletePixKeyData {
  string pix_key = 1;                      // chave
  string pix_key_creation_datetime = 2;    // dtHrCriacaoChave
  string pix_key_ownership_start_datetime = 3; // dtHrInicioPosseChave
  string claim_opening_datetime = 4;       // dtHrAberturaReivindicacao
}

// Notify Account Closure Request
message NotifyAccountClosureRequest {
  string request_id = 1;                   // Request ID for tracing
  int32 ispb = 2;                          // ispb
  string branch = 3;                       // nrAgencia
  int32 account_type = 4;                  // tpConta
  string account = 5;                      // nrConta
  int32 person_type = 6;                   // tpPessoa
  int64 document = 7;                      // cpfCnpj
  int32 reason = 8;                        // motivo
}

// Notify Account Closure Response
message NotifyAccountClosureResponse {
  ResponseStatus status = 1;
  NotifyAccountClosureData data = 2;
}

message NotifyAccountClosureData {
  string processing_datetime = 1;          // dtHrProcessamento
}

// Notify Ownership Link Closure Request
message NotifyOwnershipLinkClosureRequest {
  string request_id = 1;                   // Request ID for tracing
  int32 ispb = 2;                          // ispb
  int32 person_type = 3;                   // tpPessoa
  int64 document = 4;                      // cpfCnpj
  int32 reason = 5;                        // motivo
}

// Notify Ownership Link Closure Response
message NotifyOwnershipLinkClosureResponse {
  ResponseStatus status = 1;
  NotifyOwnershipLinkClosureData data = 2;
}

message NotifyOwnershipLinkClosureData {
  string processing_datetime = 1;          // dtHrProcessamento
}

// List PIX Keys Request
message ListPixKeysRequest {
  string request_id = 1;                   // Request ID for tracing
  int32 ispb = 2;                          // ispb
  string branch = 3;                       // nrAgencia
  int32 account_type = 4;                  // tpConta
  string account = 5;                      // nrConta
  int32 person_type = 6;                   // tpPessoa
  int64 document = 7;                      // cpfCnpj
}

// List PIX Keys Response
message ListPixKeysResponse {
  ResponseStatus status = 1;
  ListPixKeysData data = 2;
}

message ListPixKeysData {
  string jd_api_return_datetime = 1;       // dtHrJdPi
  repeated AssociatedPixKey pix_keys = 2;  // chavesAssociadas
}

message AssociatedPixKey {
  string pix_key = 1;                      // chave
}

// Query PIX Key Request
message QueryPixKeyRequest {
  string request_id = 1;                   // Request ID for tracing
  string end_to_end_id = 2;                // endToEndId
  string player_id = 3;                    // playerId
  string pix_key = 4;                      // chave
}

// Query PIX Key Response
message QueryPixKeyResponse {
  ResponseStatus status = 1;
  QueryPixKeyData data = 2;
}

message QueryPixKeyData {
  int32 pix_key_type = 1;                  // tpChave
  string pix_key = 2;                      // chave
  int32 ispb = 3;                          // ispb
  string branch = 4;                       // nrAgencia
  int32 account_type = 5;                  // tpConta
  string account = 6;                      // nrConta
  string account_opening_datetime = 7;     // dtHrAberturaConta
  int32 person_type = 8;                   // tpPessoa
  int64 document = 9;                      // cpfCnpj
  string name = 10;                        // nome
  string trade_name = 11;                  // nomeFantasia
  string pix_key_creation_datetime = 12;   // dtHrCriacaoChave
  string pix_key_ownership_start_datetime = 13; // dtHrInicioPosseChave
  string claim_opening_datetime = 14;      // dtHrAberturaReivindicacao
  string end_to_end_id = 15;               // endToEndId
  Statistics statistics = 16;              // estatisticas
}

message Statistics {
  string last_anti_fraud_update_datetime = 1; // dtHrUltAtuAntiFraude
  repeated Counter counters = 2;           // contadores
}

message Counter {
  int32 type = 1;                          // tipo
  int32 aggregated = 2;                    // agregado
  int32 d3 = 3;                            // d3
  int32 d30 = 4;                           // d30
  int32 m6 = 5;                            // m6
}

// Verify PIX Key Existence Request
message VerifyPixKeyExistenceRequest {
  string request_id = 1;                   // Request ID for tracing
  int32 ispb = 2;                          // ispb
  repeated PixKeyToVerify pix_keys_to_verify = 3; // verificarChaves
}

message PixKeyToVerify {
  string pix_key = 1;                      // chave
}

// Verify PIX Key Existence Response
message VerifyPixKeyExistenceResponse {
  ResponseStatus status = 1;
  VerifyPixKeyExistenceData data = 2;
}

message VerifyPixKeyExistenceData {
  string dict_return_datetime = 1;         // dtHrRetornoDict
  string correlation_id = 2;               // idCorrelacao
  repeated VerifiedPixKey verified_pix_keys = 3; // chavesVerificadas
}

message VerifiedPixKey {
  string pix_key = 1;                      // chave
  bool exists_in_dict = 2;                 // existeDict
}
