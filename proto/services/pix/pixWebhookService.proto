syntax = "proto3";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixWebhookService {
    rpc PixInValidation (PixInValidationRequest) returns (PixInValidationResponse);
    rpc PixInConfirm (PixInConfirmRequest) returns (PixInConfirmResponse);

    rpc PixRefundConfirm (PixRefundConfirmRequest) returns (PixRefundConfirmResponse);
}

message PixInValidationRequest {}
message PixInValidationResponse {}

message PixInConfirmRequest {}
message PixInConfirmResponse {}

message PixRefundConfirmRequest {}
message PixRefundConfirmResponse {}