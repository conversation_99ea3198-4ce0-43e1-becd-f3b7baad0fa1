syntax = "proto3";

import "services/pix/request/pixTransactionServiceRequest.proto";
import "services/pix/response/pixTransactionServiceResponse.proto";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixPaymentService {
    rpc PixOutConfirm (request.PixOutConfirmRequest) returns (response.PixOutConfirmResponse);
    rpc PixTransactionGetByEndToEndId (request.PixTransactionGetByEndToEndIdRequest) returns (response.PixTransactionGetByEndToEndIdResponse);
}