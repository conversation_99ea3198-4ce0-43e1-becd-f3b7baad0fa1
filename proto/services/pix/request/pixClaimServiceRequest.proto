syntax = "proto3";

package request;

import "wallet/banking.proto";
import "wallet/pixKey.proto";
import "services/pix/request/commonRequest.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message PixClaimCreateRequest {
    request.Header header = 1;

    int32 ispb = 2;
    wallet.PixKey pix_key = 3;
    wallet.BankAccount bank_account = 4;
    wallet.BankAccountHolder bank_account_holder = 5;
    wallet.PixKeyCreateReason reason = 6;
}

message PixClaimCancelRequest {
    request.Header header = 1;
}

message PixClaimConfirmRequest {
    request.Header header = 1;
}

message PixClaimListByAccountRequest {
    request.Header header = 1;

    int32 ispb = 2;
    wallet.PixKey pix_key = 3;
    wallet.BankAccount bank_account = 4;
    wallet.BankAccountHolder bank_account_holder = 5;
    wallet.PixKeyCreateReason reason = 6;
}