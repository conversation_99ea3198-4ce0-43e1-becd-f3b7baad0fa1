syntax = "proto3";

package request;

import "google/protobuf/timestamp.proto";
import "services/pix/request/commonRequest.proto";
import "wallet/banking.proto";
import "wallet/pixKey.proto";
import "wallet/pixTransaction.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message PixOutConfirmRequest {
    request.Header header = 1;

    string request_id = 2;

    google.protobuf.Timestamp request_time = 3;
    wallet.PixPayoutType payout_type = 4;
    wallet.PixPayoutPurpose payout_purpose = 5;

    int32 initiator_CNPJ = 6;

    wallet.BankAccount payer_bank_account = 7;
    wallet.BankAccountHolder payer_bank_account_holder = 8;

    wallet.BankAccount payee_bank_account = 9;
    wallet.BankAccountHolder payee_bank_account_holder = 10;
    wallet.PixKey payee_pix_key = 11;

    double amount = 12;
    string end_to_end_id = 13;
    string txid = 14;

}

message PixTransactionGetByEndToEndIdRequest {
    request.Header header = 1;

    string request_id = 2;
}