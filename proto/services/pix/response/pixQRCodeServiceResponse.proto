syntax = "proto3";

import "services/pix/response/commonResponse.proto";
import "wallet/pixTransaction.proto";
import "google/protobuf/timestamp.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

/**
 *
 */
message QRCodeStaticCreateResponse {
    oneof result {
        QRCodeStaticCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message QRCodeStaticCreateResponseSuccess {
    string qrcode_image_base64 = 1;
    string qrcode_payload_base64 = 2;
}

/**
 *
 */
message QRCodeDynamicCreateResponse {
    oneof result {
        QRCodeDynamicCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message QRCodeDynamicCreateResponseSuccess {
    string qrcode_image_base64 = 1;
    string qrcode_payload_base64 = 2;
    string payloadJws = 3;
    string documentId = 4;
}

/**
 *
 */
message QRCodeDynamicDecodeByUrlResponse {
    oneof result {
        QRCodeDynamicDecodeByUrlResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message QRCodeDynamicDecodeByUrlResponseSuccess {
    string end_to_end_id = 1;
    wallet.QRCodeResponseType format = 2;

    //    dados_qrcode_dynamic = 3;
}

/**
 *
 */
message QRCodeGetByPayloadResponse {
    oneof result {
        QRCodeGetByPayloadResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message QRCodeGetByPayloadResponseSuccess {
    string end_to_end_id = 1;
    wallet.QRCodeResponseType format = 2;

//    dados_qrcode_static = 3;
//    dados_qrcode_dynamic = 4;
//    google.protobuf.Timestamp key_possession_start_datetime = 5;    // The

}