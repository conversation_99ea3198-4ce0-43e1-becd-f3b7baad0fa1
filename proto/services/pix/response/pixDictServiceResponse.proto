syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "services/pix/response/commonResponse.proto";
import "wallet/pixKey.proto";
import "wallet/banking.proto";

package response; // Nome do pacote Protobuf para os serviços

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

/**
 * Create PIX KEY Response
 */
message PixKeyCreateResponse {
    oneof result {
        PixKeyCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyCreateResponseSuccess {
    wallet.PixKey pix_key = 2;                                      // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
    google.protobuf.Timestamp key_creation_datetime = 3;            // The date and time when the Pix key was created.
    google.protobuf.Timestamp key_possession_start_datetime = 4;    // The date and time when the possession of the Pix key started.
}


/**
 * Delete PIX KEY Response
 */
message PixKeyDeleteResponse {
    oneof result {
        PixKeyDeleteResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyDeleteResponseSuccess {
    wallet.PixKey pix_key = 1;                                      // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
    google.protobuf.Timestamp key_creation_datetime = 2;            // The date and time when the Pix key was created.
    google.protobuf.Timestamp key_possession_start_datetime = 3;    // The date and time when the possession of the Pix key started.
}

/**
 * Update PIX KEY Response
 */
message PixKeyUpdateResponse {
    oneof result {
        PixKeyUpdateResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyUpdateResponseSuccess {
    wallet.PixKey pix_key = 2;                                      // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
    google.protobuf.Timestamp key_creation_datetime = 3;            // The date and time when the Pix key was created.
    google.protobuf.Timestamp key_possession_start_datetime = 4;    // The date and time when the possession of the Pix key started.
}

/**
 * List PIX KEY Response
 */
message PixKeyListByAccountResponse {
    oneof result {
        PixKeyListByAccountResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyListByAccountResponseSuccess {
    repeated wallet.PixKey pix_key = 1;
}

/**
 * Check if Exists PIX KEY Response
 */
message PixKeyIsExistResponse {
    oneof result {
        PixKeyIsExistResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyIsExistResponseSuccess {
    repeated wallet.PixKey existent_pix_key = 2;
}

/**
 * Get PIX KEY Response
 */
message PixKeyGetResponse {
    oneof result {
        PixKeyIsExistResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyGetResponseSuccess {
    wallet.PixKey pix_key = 2;
    wallet.BankAccount bank_account = 3;
    wallet.BankAccountHolder bank_account_holder = 4;
}

/**
 * Remove PIX Keys By BankAccount
 */
message NotifyAccountClosureResponse {
    oneof result {
        NotifyAccountClosureResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message NotifyAccountClosureResponseSuccess {
    repeated wallet.PixKey pix_key = 2;
}