syntax = "proto3";

package request;

import "wallet/banking.proto";
import "wallet/pixKey.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

message Error {
    ErrorCode error = 1;
    string error_subcode = 2;
    string error_details = 3;
    string error_message = 4;
}

enum ErrorCode {
    INTERNAL_ERROR = 0;
    ERROR_CHANNEL_REQUEST = 1;
    ERROR_CHANNEL_BUSINESS = 2;
}