syntax = "proto3";

import "services/pix/response/commonResponse.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

/**
 *
 */
message PixOutConfirmResponse {
    oneof result {
        PixOutConfirmResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message PixOutConfirmResponseSuccess {

}

/**
 *
 */
message PixTransactionGetByEndToEndIdResponse {
    oneof result {
        PixTransactionGetByEndToEndIdResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message PixTransactionGetByEndToEndIdResponseSuccess {}