syntax = "proto3";

package wallet;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";

enum AccountHolderTypeType {
    NATURAL = 0;      // Brazilian Individual Taxpayer Registry, max. 11 digits
    LEGAL = 1;
}

enum BankAccountType {
    CHECKING_ACCOUNT = 0;   // Used for day-to-day transactions.
    SALARY_ACCOUNT = 1;     // Specifically for receiving salary payments.
    SAVINGS_ACCOUNT = 2;    // Used for saving money, typically with lower transaction fees.
    PAYMENT_ACCOUNT = 3;    // Often associated with digital wallets and fintechs.
}


message BankAccount {
    int32 ispb = 1;
    string branch_code = 2; // The branch code of the bank account.
    string account_number = 3;
    BankAccountType account_type = 4;
    string status = 5;
    google.protobuf.Timestamp account_opening_datetime = 6;
}

message BankAccountHolder {
    AccountHolderTypeType holder_type = 1;
    string holder_name = 2; // Account Owner Name
    string holder_nickname = 3; // Account Owner Nickname
    string document_id = 4;
}