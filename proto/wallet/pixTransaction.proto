syntax = "proto3";

package wallet;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";

/*
 * CommonResult represents a generic operation outcome.
 * It is typically used in RPC API responses to signal success
 *  - when no specific error message or detailed failure information is provided.
 */
enum QRCodeResponseType {
    QRCODE_RESPONSE_TYPE_IMAGE_ONLY = 0;
    QRCODE_RESPONSE_TYPE_PAYLOAD_ONLY = 1;
    QRCODE_RESPONSE_TYPE_BOTH = 3;
}

enum QRCodeType {
    QRCODE_STATIC = 0;
    QRCODE_DYNAMIC_INSTANT_PAYMENT = 1;
    QRCODE_DYNAMIC_EXPIRE_PAYMENT = 2;
    QRCODE_DYNAMIC_COMBINED_PAYMENT = 3;
}

enum PixPayoutType {
    PIX_PAYOUT_TYPE_MANUAL = 0;
    PIX_PAYOUT_TYPE_KEY = 1;
    PIX_PAYOUT_TYPE_QRCODE_STATIC = 2;
    PIX_PAYOUT_TYPE_QRCODE_DYNAMIC = 3;
}

enum PixPayoutPurpose {
    PIX_PAYOUT_PURPOSE_BUY_OR_TRANSFER = 0;
}