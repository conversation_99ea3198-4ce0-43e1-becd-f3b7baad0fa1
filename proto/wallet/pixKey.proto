syntax = "proto3";

package wallet;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";


/*
 * PixKeyCreateResult defines the possible outcomes when attempting to create a Pix Key.
 */
enum PixKeyCreateResult {
    CREATE_PIX_SUCCESS = 0;                  // The Pix Key was created successfully.
    CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON = 1; // The Pix Key already exists and is owned by another person.
    CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT = 2; // The Pix Key already exists and is linked to another financial institution.
}

/*
 * PixKeyType defines the valid types of Pix Keys.
 */
enum PixKeyType {
    CPF = 0;      // Brazilian Individual Taxpayer Registry, max. 11 digits.
    CNPJ = 1;     // Brazilian Company Taxpayer Registry, max. 14 digits.
    EMAIL = 2;    // Email address, max. 72 characters.
    PHONE = 3;    // Phone number, including country and area code (e.g., +5511987654321).
    EVP = 4;      // EVP - Virtual Payment Address - a random key generated by the Pix system.
}

/*
 * PixKey represents the fundamental structure of a Pix Key.
 */
message PixKey {
    PixKeyType key_type = 1; // The type of the Pix Key (e.g., CPF, EMAIL).
    string key_value = 2;    // The actual value of the Pix Key (e.g., "123.456.789-00", "<EMAIL>").
}

/*
 * PixKeyCreateReason defines the reasons for creating a Pix Key.
 */
enum PixKeyCreateReason {
    CREATE_PIXKEY_REQUEST = 0;         // The Pix Key creation was initiated by a client request.
    CREATE_PIXKEY_RECONCILIATION = 6;  // The Pix Key creation is part of a reconciliation process.
}

/*
 * PixKeyUpdateReason defines the reasons for updating a Pix Key.
 */
enum PixKeyUpdateReason {
    UPDATE_PIXKEY_REQUEST = 0;           // The Pix Key update was initiated by a client request.
    UPDATE_PIX_KEY_ACCOUNT_CHANGE = 2;   // The Pix Key is being updated due to a change in the client's associated account number.
    UPDATE_PIXKEY_RECONCILIATION = 6;    // The Pix Key update is part of a reconciliation process.
}

/*
 * PixKeyDeleteReason defines the reasons for deleting a Pix Key.
 */
enum PixKeyDeleteReason {
    DELETE_CLIENT_REQUEST = 0;           // The Pix Key deletion was initiated by a client request.
    DELETE_FRAUD = 4;                    // The Pix Key was deleted due to detected fraudulent activity.
    DELETE_RECONCILIATION = 6;           // The Pix Key deletion is part of a reconciliation process.
    DELETE_FEDERAL_REVENUE_VALIDATION = 8; // The Pix Key was deleted due to a validation by a government authority (e.g., Federal Revenue).
}

/*
 * PixKeyClaimStatus defines the possible statuses for a Pix Key claim process.
 */
enum PixKeyClaimStatus {
    PIXKEY_CLAIM_STATUS_OPEN = 0; // Status whenever a pixClaim task is created.
    PIXKEY_CLAIM_STATUS_ACK = 1; // Current PixKey Owner ACK he has received the claim.
    PIXKEY_CLAIM_STATUS_CONFIRMED = 2; // Current PixKey Owner agrees to give the PixKey away
    PIXKEY_CLAIM_STATUS_CANCELLED = 3; // Either Requester or Owner cancel the claim
    PIXKEY_CLAIM_STATUS_SUCCESS = 4; // PixKey Owner agrees to give, and requester confirms
}

enum PixKeyBucketPolicy {
    PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN = 0;
    PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN_V2 = 1;
    PIXKEY_POLICY_ENTRIES_READ_PARTICIPANT_ANTISCAN = 2;
    PIXKEY_POLICY_ENTRIES_STATISTICS_READ = 3;
    PIXKEY_POLICY_ENTRIES_WRITE = 4;
    PIXKEY_POLICY_ENTRIES_UPDATE = 5;
    PIXKEY_POLICY_CLAIMS_READ = 6;
    PIXKEY_POLICY_CLAIMS_WRITE = 7;
    PIXKEY_POLICY_CLAIMS_LIST_WITH_ROLE = 8;
    PIXKEY_POLICY_CLAIMS_LIST_WITHOUT_ROLE = 9;
    PIXKEY_POLICY_SYNC_VERIFICATIONS_WRITE = 10;
    PIXKEY_POLICY_CIDS_FILES_WRITE = 11;
    PIXKEY_POLICY_CIDS_FILES_READ = 12;
    PIXKEY_POLICY_CIDS_EVENTS_LIST = 13;
    PIXKEY_POLICY_CIDS_ENTRIES_READ = 14;
    PIXKEY_POLICY_INFRACTION_REPORTS_READ = 15;
    PIXKEY_POLICY_INFRACTION_REPORTS_WRITE = 16;
    PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITH_ROLE = 17;
    PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITHOUT_ROLE = 18;
    PIXKEY_POLICY_KEYS_CHECK = 19;
    PIXKEY_POLICY_REFUNDS_READ = 20;
    PIXKEY_POLICY_REFUNDS_WRITE = 21;
    PIXKEY_POLICY_REFUND_LIST_WITH_ROLE = 22;
    PIXKEY_POLICY_REFUND_LIST_WITHOUT_ROLE = 23;
    PIXKEY_POLICY_FRAUD_MARKERS_READ = 24;
    PIXKEY_POLICY_FRAUD_MARKERS_WRITE = 25;
    PIXKEY_POLICY_FRAUD_MARKERS_LIST = 26;
    PIXKEY_POLICY_PERSONS_STATISTICS_READ = 27;
    PIXKEY_POLICY_POLICIES_READ = 28;
    PIXKEY_POLICY_POLICIES_LIST = 29;
}

message PixKeyPolicy {
    PixKeyBucketPolicy policy_name = 1;
    int32 token_available = 2;
    int32 token_capacity = 3;
    int32 token_refresh_amount = 4;
    int32 token_refresh_time = 5;
}