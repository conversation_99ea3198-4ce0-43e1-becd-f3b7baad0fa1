// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pixQRCodeService.proto

package services

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pixQRCodeService_proto protoreflect.FileDescriptor

const file_services_pixQRCodeService_proto_rawDesc = "" +
	"\n" +
	"\x1fservices/pixQRCodeService.proto\x12\bservices\x1a.services/request/pixQRCodeServiceRequest.proto\x1a0services/response/pixQRCodeServiceResponse.proto2\xd2\x01\n" +
	"\x10PixQRCodeService\x12^\n" +
	"\x12QRCodeStaticCreate\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticCreateResponse\x12^\n" +
	"\x12QRCodeStaticDecode\x12\".request.QRCodeStaticDecodeRequest\x1a$.response.QRCodeStaticDecodeResponseBAZ?gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/servicesb\x06proto3"

var file_services_pixQRCodeService_proto_goTypes = []any{
	(*request.QRCodeStaticCreateRequest)(nil),   // 0: request.QRCodeStaticCreateRequest
	(*request.QRCodeStaticDecodeRequest)(nil),   // 1: request.QRCodeStaticDecodeRequest
	(*response.QRCodeStaticCreateResponse)(nil), // 2: response.QRCodeStaticCreateResponse
	(*response.QRCodeStaticDecodeResponse)(nil), // 3: response.QRCodeStaticDecodeResponse
}
var file_services_pixQRCodeService_proto_depIdxs = []int32{
	0, // 0: services.PixQRCodeService.QRCodeStaticCreate:input_type -> request.QRCodeStaticCreateRequest
	1, // 1: services.PixQRCodeService.QRCodeStaticDecode:input_type -> request.QRCodeStaticDecodeRequest
	2, // 2: services.PixQRCodeService.QRCodeStaticCreate:output_type -> response.QRCodeStaticCreateResponse
	3, // 3: services.PixQRCodeService.QRCodeStaticDecode:output_type -> response.QRCodeStaticDecodeResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pixQRCodeService_proto_init() }
func file_services_pixQRCodeService_proto_init() {
	if File_services_pixQRCodeService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pixQRCodeService_proto_rawDesc), len(file_services_pixQRCodeService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pixQRCodeService_proto_goTypes,
		DependencyIndexes: file_services_pixQRCodeService_proto_depIdxs,
	}.Build()
	File_services_pixQRCodeService_proto = out.File
	file_services_pixQRCodeService_proto_goTypes = nil
	file_services_pixQRCodeService_proto_depIdxs = nil
}
