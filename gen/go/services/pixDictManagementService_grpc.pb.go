// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pixParticipantService.proto

package services

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixDictManagementService_PixKeyPolicy_FullMethodName       = "/services.PixDictManagementService/PixKeyPolicy"
	PixDictManagementService_PixKeyPolicyStatus_FullMethodName = "/services.PixDictManagementService/PixKeyPolicyStatus"
)

// PixDictManagementServiceClient is the client API for PixDictManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixDictManagementServiceClient interface {
	PixKeyPolicy(ctx context.Context, in *request.PixKeyPolicyRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyResponse, error)
	PixKeyPolicyStatus(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error)
}

type pixDictManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixDictManagementServiceClient(cc grpc.ClientConnInterface) PixDictManagementServiceClient {
	return &pixDictManagementServiceClient{cc}
}

func (c *pixDictManagementServiceClient) PixKeyPolicy(ctx context.Context, in *request.PixKeyPolicyRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyPolicyResponse)
	err := c.cc.Invoke(ctx, PixDictManagementService_PixKeyPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictManagementServiceClient) PixKeyPolicyStatus(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyPolicyStatusResponse)
	err := c.cc.Invoke(ctx, PixDictManagementService_PixKeyPolicyStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixDictManagementServiceServer is the server API for PixDictManagementService service.
// All implementations must embed UnimplementedPixDictManagementServiceServer
// for forward compatibility.
type PixDictManagementServiceServer interface {
	PixKeyPolicy(context.Context, *request.PixKeyPolicyRequest) (*response.PixKeyPolicyResponse, error)
	PixKeyPolicyStatus(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error)
	mustEmbedUnimplementedPixDictManagementServiceServer()
}

// UnimplementedPixDictManagementServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixDictManagementServiceServer struct{}

func (UnimplementedPixDictManagementServiceServer) PixKeyPolicy(context.Context, *request.PixKeyPolicyRequest) (*response.PixKeyPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyPolicy not implemented")
}
func (UnimplementedPixDictManagementServiceServer) PixKeyPolicyStatus(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyPolicyStatus not implemented")
}
func (UnimplementedPixDictManagementServiceServer) mustEmbedUnimplementedPixDictManagementServiceServer() {
}
func (UnimplementedPixDictManagementServiceServer) testEmbeddedByValue() {}

// UnsafePixDictManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixDictManagementServiceServer will
// result in compilation errors.
type UnsafePixDictManagementServiceServer interface {
	mustEmbedUnimplementedPixDictManagementServiceServer()
}

func RegisterPixDictManagementServiceServer(s grpc.ServiceRegistrar, srv PixDictManagementServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixDictManagementServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixDictManagementService_ServiceDesc, srv)
}

func _PixDictManagementService_PixKeyPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictManagementServiceServer).PixKeyPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictManagementService_PixKeyPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictManagementServiceServer).PixKeyPolicy(ctx, req.(*request.PixKeyPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictManagementService_PixKeyPolicyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyPolicyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictManagementServiceServer).PixKeyPolicyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictManagementService_PixKeyPolicyStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictManagementServiceServer).PixKeyPolicyStatus(ctx, req.(*request.PixKeyPolicyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixDictManagementService_ServiceDesc is the grpc.ServiceDesc for PixDictManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixDictManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixDictManagementService",
	HandlerType: (*PixDictManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixKeyPolicy",
			Handler:    _PixDictManagementService_PixKeyPolicy_Handler,
		},
		{
			MethodName: "PixKeyPolicyStatus",
			Handler:    _PixDictManagementService_PixKeyPolicyStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pixParticipantService.proto",
}