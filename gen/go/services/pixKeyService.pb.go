// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pixDictService.proto

package services

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pixKeyService_proto protoreflect.FileDescriptor

const file_services_pixKeyService_proto_rawDesc = "" +
	"\n" +
	"\x1cservices/pixDictService.proto\x12\bservices\x1a+services/request/pixDictServiceRequest.proto\x1a-services/response/pixKeyServiceResponse.proto2\xa7\x03\n" +
	"\rPixKeyService\x12L\n" +
	"\fPixKeyCreate\x12\x1c.request.CreatePixKeyRequest\x1a\x1e.response.CreatePixKeyResponse\x12L\n" +
	"\fPixKeyDelete\x12\x1c.request.DeletePixKeyRequest\x1a\x1e.response.DeletePixKeyResponse\x12L\n" +
	"\fPixKeyUpdate\x12\x1c.request.UpdatePixKeyRequest\x1a\x1e.response.UpdatePixKeyResponse\x12a\n" +
	"\x13PixKeyListByAccount\x12#.request.PixKeyListByAccountRequest\x1a%.response.PixKeyListByAccountResponse\x12I\n" +
	"\vPixKeyClaim\x12\x1b.request.PixKeyClaimRequest\x1a\x1d.response.PixKeyClaimResponseBAZ?gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/servicesb\x06proto3"

var file_services_pixKeyService_proto_goTypes = []any{
	(*request.CreatePixKeyRequest)(nil),          // 0: request.CreatePixKeyRequest
	(*request.DeletePixKeyRequest)(nil),          // 1: request.DeletePixKeyRequest
	(*request.UpdatePixKeyRequest)(nil),          // 2: request.UpdatePixKeyRequest
	(*request.PixKeyListByAccountRequest)(nil),   // 3: request.PixKeyListByAccountRequest
	(*request.PixKeyClaimRequest)(nil),           // 4: request.PixKeyClaimRequest
	(*response.CreatePixKeyResponse)(nil),        // 5: response.CreatePixKeyResponse
	(*response.DeletePixKeyResponse)(nil),        // 6: response.DeletePixKeyResponse
	(*response.UpdatePixKeyResponse)(nil),        // 7: response.UpdatePixKeyResponse
	(*response.PixKeyListByAccountResponse)(nil), // 8: response.PixKeyListByAccountResponse
	(*response.PixKeyClaimResponse)(nil),         // 9: response.PixKeyClaimResponse
}
var file_services_pixKeyService_proto_depIdxs = []int32{
	0, // 0: services.PixKeyService.PixKeyCreate:input_type -> request.CreatePixKeyRequest
	1, // 1: services.PixKeyService.PixKeyDelete:input_type -> request.DeletePixKeyRequest
	2, // 2: services.PixKeyService.PixKeyUpdate:input_type -> request.UpdatePixKeyRequest
	3, // 3: services.PixKeyService.PixKeyListByAccount:input_type -> request.PixKeyListByAccountRequest
	4, // 4: services.PixKeyService.PixKeyClaim:input_type -> request.PixKeyClaimRequest
	5, // 5: services.PixKeyService.PixKeyCreate:output_type -> response.CreatePixKeyResponse
	6, // 6: services.PixKeyService.PixKeyDelete:output_type -> response.DeletePixKeyResponse
	7, // 7: services.PixKeyService.PixKeyUpdate:output_type -> response.UpdatePixKeyResponse
	8, // 8: services.PixKeyService.PixKeyListByAccount:output_type -> response.PixKeyListByAccountResponse
	9, // 9: services.PixKeyService.PixKeyClaim:output_type -> response.PixKeyClaimResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pixKeyService_proto_init() }
func file_services_pixKeyService_proto_init() {
	if File_services_pixKeyService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pixKeyService_proto_rawDesc), len(file_services_pixKeyService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pixKeyService_proto_goTypes,
		DependencyIndexes: file_services_pixKeyService_proto_depIdxs,
	}.Build()
	File_services_pixKeyService_proto = out.File
	file_services_pixKeyService_proto_goTypes = nil
	file_services_pixKeyService_proto_depIdxs = nil
}