// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pixDictService.proto

package services

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixKeyService_PixKeyCreate_FullMethodName        = "/services.PixKeyService/PixKeyCreate"
	PixKeyService_PixKeyDelete_FullMethodName        = "/services.PixKeyService/PixKeyDelete"
	PixKeyService_PixKeyUpdate_FullMethodName        = "/services.PixKeyService/PixKeyUpdate"
	PixKeyService_PixKeyListByAccount_FullMethodName = "/services.PixKeyService/PixKeyListByAccount"
	PixKeyService_PixKeyClaim_FullMethodName         = "/services.PixKeyService/PixKeyClaim"
)

// PixKeyServiceClient is the client API for PixKeyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixKeyServiceClient interface {
	PixKeyCreate(ctx context.Context, in *request.CreatePixKeyRequest, opts ...grpc.CallOption) (*response.CreatePixKeyResponse, error)
	PixKeyDelete(ctx context.Context, in *request.DeletePixKeyRequest, opts ...grpc.CallOption) (*response.DeletePixKeyResponse, error)
	PixKeyUpdate(ctx context.Context, in *request.UpdatePixKeyRequest, opts ...grpc.CallOption) (*response.UpdatePixKeyResponse, error)
	PixKeyListByAccount(ctx context.Context, in *request.PixKeyListByAccountRequest, opts ...grpc.CallOption) (*response.PixKeyListByAccountResponse, error)
	PixKeyClaim(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
}

type pixKeyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixKeyServiceClient(cc grpc.ClientConnInterface) PixKeyServiceClient {
	return &pixKeyServiceClient{cc}
}

func (c *pixKeyServiceClient) PixKeyCreate(ctx context.Context, in *request.CreatePixKeyRequest, opts ...grpc.CallOption) (*response.CreatePixKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.CreatePixKeyResponse)
	err := c.cc.Invoke(ctx, PixKeyService_PixKeyCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixKeyServiceClient) PixKeyDelete(ctx context.Context, in *request.DeletePixKeyRequest, opts ...grpc.CallOption) (*response.DeletePixKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.DeletePixKeyResponse)
	err := c.cc.Invoke(ctx, PixKeyService_PixKeyDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixKeyServiceClient) PixKeyUpdate(ctx context.Context, in *request.UpdatePixKeyRequest, opts ...grpc.CallOption) (*response.UpdatePixKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.UpdatePixKeyResponse)
	err := c.cc.Invoke(ctx, PixKeyService_PixKeyUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixKeyServiceClient) PixKeyListByAccount(ctx context.Context, in *request.PixKeyListByAccountRequest, opts ...grpc.CallOption) (*response.PixKeyListByAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyListByAccountResponse)
	err := c.cc.Invoke(ctx, PixKeyService_PixKeyListByAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixKeyServiceClient) PixKeyClaim(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixKeyService_PixKeyClaim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixKeyServiceServer is the server API for PixKeyService service.
// All implementations must embed UnimplementedPixKeyServiceServer
// for forward compatibility.
type PixKeyServiceServer interface {
	PixKeyCreate(context.Context, *request.CreatePixKeyRequest) (*response.CreatePixKeyResponse, error)
	PixKeyDelete(context.Context, *request.DeletePixKeyRequest) (*response.DeletePixKeyResponse, error)
	PixKeyUpdate(context.Context, *request.UpdatePixKeyRequest) (*response.UpdatePixKeyResponse, error)
	PixKeyListByAccount(context.Context, *request.PixKeyListByAccountRequest) (*response.PixKeyListByAccountResponse, error)
	PixKeyClaim(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error)
	mustEmbedUnimplementedPixKeyServiceServer()
}

// UnimplementedPixKeyServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixKeyServiceServer struct{}

func (UnimplementedPixKeyServiceServer) PixKeyCreate(context.Context, *request.CreatePixKeyRequest) (*response.CreatePixKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyCreate not implemented")
}
func (UnimplementedPixKeyServiceServer) PixKeyDelete(context.Context, *request.DeletePixKeyRequest) (*response.DeletePixKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyDelete not implemented")
}
func (UnimplementedPixKeyServiceServer) PixKeyUpdate(context.Context, *request.UpdatePixKeyRequest) (*response.UpdatePixKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyUpdate not implemented")
}
func (UnimplementedPixKeyServiceServer) PixKeyListByAccount(context.Context, *request.PixKeyListByAccountRequest) (*response.PixKeyListByAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyListByAccount not implemented")
}
func (UnimplementedPixKeyServiceServer) PixKeyClaim(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaim not implemented")
}
func (UnimplementedPixKeyServiceServer) mustEmbedUnimplementedPixKeyServiceServer() {}
func (UnimplementedPixKeyServiceServer) testEmbeddedByValue()                       {}

// UnsafePixKeyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixKeyServiceServer will
// result in compilation errors.
type UnsafePixKeyServiceServer interface {
	mustEmbedUnimplementedPixKeyServiceServer()
}

func RegisterPixKeyServiceServer(s grpc.ServiceRegistrar, srv PixKeyServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixKeyServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixKeyService_ServiceDesc, srv)
}

func _PixKeyService_PixKeyCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.CreatePixKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixKeyServiceServer).PixKeyCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixKeyService_PixKeyCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixKeyServiceServer).PixKeyCreate(ctx, req.(*request.CreatePixKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixKeyService_PixKeyDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.DeletePixKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixKeyServiceServer).PixKeyDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixKeyService_PixKeyDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixKeyServiceServer).PixKeyDelete(ctx, req.(*request.DeletePixKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixKeyService_PixKeyUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.UpdatePixKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixKeyServiceServer).PixKeyUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixKeyService_PixKeyUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixKeyServiceServer).PixKeyUpdate(ctx, req.(*request.UpdatePixKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixKeyService_PixKeyListByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyListByAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixKeyServiceServer).PixKeyListByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixKeyService_PixKeyListByAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixKeyServiceServer).PixKeyListByAccount(ctx, req.(*request.PixKeyListByAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixKeyService_PixKeyClaim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixKeyServiceServer).PixKeyClaim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixKeyService_PixKeyClaim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixKeyServiceServer).PixKeyClaim(ctx, req.(*request.PixKeyClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixKeyService_ServiceDesc is the grpc.ServiceDesc for PixKeyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixKeyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixKeyService",
	HandlerType: (*PixKeyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixKeyCreate",
			Handler:    _PixKeyService_PixKeyCreate_Handler,
		},
		{
			MethodName: "PixKeyDelete",
			Handler:    _PixKeyService_PixKeyDelete_Handler,
		},
		{
			MethodName: "PixKeyUpdate",
			Handler:    _PixKeyService_PixKeyUpdate_Handler,
		},
		{
			MethodName: "PixKeyListByAccount",
			Handler:    _PixKeyService_PixKeyListByAccount_Handler,
		},
		{
			MethodName: "PixKeyClaim",
			Handler:    _PixKeyService_PixKeyClaim_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pixDictService.proto",
}