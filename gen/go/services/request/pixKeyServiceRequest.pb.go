// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/request/pixDictServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreatePixKeyRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreatePixKeyRequest) Reset() {
	*x = CreatePixKeyRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePixKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePixKeyRequest) ProtoMessage() {}

func (x *CreatePixKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePixKeyRequest.ProtoReflect.Descriptor instead.
func (*CreatePixKeyRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePixKeyRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreatePixKeyRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *CreatePixKeyRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *CreatePixKeyRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *CreatePixKeyRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *CreatePixKeyRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type DeletePixKeyRequest struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	RequestId     string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb          int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey        *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	Reason        wallet.PixKeyDeleteReason `protobuf:"varint,4,opt,name=reason,proto3,enum=wallet.PixKeyDeleteReason" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePixKeyRequest) Reset() {
	*x = DeletePixKeyRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePixKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePixKeyRequest) ProtoMessage() {}

func (x *DeletePixKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePixKeyRequest.ProtoReflect.Descriptor instead.
func (*DeletePixKeyRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *DeletePixKeyRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DeletePixKeyRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *DeletePixKeyRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *DeletePixKeyRequest) GetReason() wallet.PixKeyDeleteReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyDeleteReason(0)
}

type UpdatePixKeyRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,5,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,6,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,7,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdatePixKeyRequest) Reset() {
	*x = UpdatePixKeyRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePixKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePixKeyRequest) ProtoMessage() {}

func (x *UpdatePixKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePixKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdatePixKeyRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePixKeyRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *UpdatePixKeyRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *UpdatePixKeyRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *UpdatePixKeyRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *UpdatePixKeyRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *UpdatePixKeyRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type PixKeyListByAccountRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,3,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,4,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyListByAccountRequest) Reset() {
	*x = PixKeyListByAccountRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyListByAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyListByAccountRequest) ProtoMessage() {}

func (x *PixKeyListByAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyListByAccountRequest.ProtoReflect.Descriptor instead.
func (*PixKeyListByAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{3}
}

func (x *PixKeyListByAccountRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyListByAccountRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyListByAccountRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyListByAccountRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

type PixKeyClaimRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyClaimRequest) Reset() {
	*x = PixKeyClaimRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimRequest) ProtoMessage() {}

func (x *PixKeyClaimRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimRequest.ProtoReflect.Descriptor instead.
func (*PixKeyClaimRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{4}
}

func (x *PixKeyClaimRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyClaimRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyClaimRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyClaimRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyClaimRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyClaimRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type PixKeyClaimListRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyClaimListRequest) Reset() {
	*x = PixKeyClaimListRequest{}
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimListRequest) ProtoMessage() {}

func (x *PixKeyClaimListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixKeyServiceRequest_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimListRequest.ProtoReflect.Descriptor instead.
func (*PixKeyClaimListRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixKeyServiceRequest_proto_rawDescGZIP(), []int{5}
}

func (x *PixKeyClaimListRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyClaimListRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyClaimListRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

var File_services_request_pixKeyServiceRequest_proto protoreflect.FileDescriptor

const file_services_request_pixKeyServiceRequest_proto_rawDesc = "" +
	"\n" +
	"+services/request/pixDictServiceRequest.proto\x12\arequest\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\"\xa7\x02\n" +
	"\x13CreatePixKeyRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xa4\x01\n" +
	"\x13DeletePixKeyRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x122\n" +
	"\x06reason\x18\x04 \x01(\x0e2\x1a.wallet.PixKeyDeleteReasonR\x06reason\"\xa7\x02\n" +
	"\x13UpdatePixKeyRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x05 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x06 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\a \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xd1\x01\n" +
	"\x1aPixKeyListByAccountRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x126\n" +
	"\fbank_account\x18\x03 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x04 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\"\xa6\x02\n" +
	"\x12PixKeyClaimRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xaa\x02\n" +
	"\x16PixKeyClaimListRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reasonBIZGgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/requestb\x06proto3"

var (
	file_services_request_pixKeyServiceRequest_proto_rawDescOnce sync.Once
	file_services_request_pixKeyServiceRequest_proto_rawDescData []byte
)

func file_services_request_pixKeyServiceRequest_proto_rawDescGZIP() []byte {
	file_services_request_pixKeyServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_request_pixKeyServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_request_pixKeyServiceRequest_proto_rawDesc), len(file_services_request_pixKeyServiceRequest_proto_rawDesc)))
	})
	return file_services_request_pixKeyServiceRequest_proto_rawDescData
}

var file_services_request_pixKeyServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_services_request_pixKeyServiceRequest_proto_goTypes = []any{
	(*CreatePixKeyRequest)(nil),        // 0: request.CreatePixKeyRequest
	(*DeletePixKeyRequest)(nil),        // 1: request.DeletePixKeyRequest
	(*UpdatePixKeyRequest)(nil),        // 2: request.UpdatePixKeyRequest
	(*PixKeyListByAccountRequest)(nil), // 3: request.PixKeyListByAccountRequest
	(*PixKeyClaimRequest)(nil),         // 4: request.PixKeyClaimRequest
	(*PixKeyClaimListRequest)(nil),     // 5: request.PixKeyClaimListRequest
	(*wallet.PixKey)(nil),              // 6: wallet.PixKey
	(*wallet.BankAccount)(nil),         // 7: wallet.BankAccount
	(*wallet.BankAccountHolder)(nil),   // 8: wallet.BankAccountHolder
	(wallet.PixKeyCreateReason)(0),     // 9: wallet.PixKeyCreateReason
	(wallet.PixKeyDeleteReason)(0),     // 10: wallet.PixKeyDeleteReason
}
var file_services_request_pixKeyServiceRequest_proto_depIdxs = []int32{
	6,  // 0: request.CreatePixKeyRequest.pix_key:type_name -> wallet.PixKey
	7,  // 1: request.CreatePixKeyRequest.bank_account:type_name -> wallet.BankAccount
	8,  // 2: request.CreatePixKeyRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	9,  // 3: request.CreatePixKeyRequest.reason:type_name -> wallet.PixKeyCreateReason
	6,  // 4: request.DeletePixKeyRequest.pix_key:type_name -> wallet.PixKey
	10, // 5: request.DeletePixKeyRequest.reason:type_name -> wallet.PixKeyDeleteReason
	6,  // 6: request.UpdatePixKeyRequest.pix_key:type_name -> wallet.PixKey
	7,  // 7: request.UpdatePixKeyRequest.bank_account:type_name -> wallet.BankAccount
	8,  // 8: request.UpdatePixKeyRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	9,  // 9: request.UpdatePixKeyRequest.reason:type_name -> wallet.PixKeyCreateReason
	7,  // 10: request.PixKeyListByAccountRequest.bank_account:type_name -> wallet.BankAccount
	8,  // 11: request.PixKeyListByAccountRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	6,  // 12: request.PixKeyClaimRequest.pix_key:type_name -> wallet.PixKey
	7,  // 13: request.PixKeyClaimRequest.bank_account:type_name -> wallet.BankAccount
	8,  // 14: request.PixKeyClaimRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	9,  // 15: request.PixKeyClaimRequest.reason:type_name -> wallet.PixKeyCreateReason
	6,  // 16: request.PixKeyClaimListRequest.pix_key:type_name -> wallet.PixKey
	7,  // 17: request.PixKeyClaimListRequest.bank_account:type_name -> wallet.BankAccount
	8,  // 18: request.PixKeyClaimListRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	9,  // 19: request.PixKeyClaimListRequest.reason:type_name -> wallet.PixKeyCreateReason
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_services_request_pixKeyServiceRequest_proto_init() }
func file_services_request_pixKeyServiceRequest_proto_init() {
	if File_services_request_pixKeyServiceRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_request_pixKeyServiceRequest_proto_rawDesc), len(file_services_request_pixKeyServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_request_pixKeyServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_request_pixKeyServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_request_pixKeyServiceRequest_proto_msgTypes,
	}.Build()
	File_services_request_pixKeyServiceRequest_proto = out.File
	file_services_request_pixKeyServiceRequest_proto_goTypes = nil
	file_services_request_pixKeyServiceRequest_proto_depIdxs = nil
}