// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/request/pixParticipantServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyRequest) Reset() {
	*x = PixKeyPolicyRequest{}
	mi := &file_services_request_pixDictManagementServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyRequest) ProtoMessage() {}

func (x *PixKeyPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixDictManagementServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyRequest.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixDictManagementServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyPolicyRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type PixKeyPolicyStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PolicyName    *wallet.PixKeyPolicy   `protobuf:"bytes,1,opt,name=policy_name,json=policyName,proto3" json:"policy_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyStatusRequest) Reset() {
	*x = PixKeyPolicyStatusRequest{}
	mi := &file_services_request_pixDictManagementServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyStatusRequest) ProtoMessage() {}

func (x *PixKeyPolicyStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixDictManagementServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyStatusRequest.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyStatusRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixDictManagementServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyPolicyStatusRequest) GetPolicyName() *wallet.PixKeyPolicy {
	if x != nil {
		return x.PolicyName
	}
	return nil
}

var File_services_request_pixDictManagementServiceRequest_proto protoreflect.FileDescriptor

const file_services_request_pixDictManagementServiceRequest_proto_rawDesc = "" +
	"\n" +
	"6services/request/pixParticipantServiceRequest.proto\x12\arequest\x1a\x13wallet/pixKey.proto\"3\n" +
	"\x13PixKeyPolicyRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\"R\n" +
	"\x19PixKeyPolicyStatusRequest\x125\n" +
	"\vpolicy_name\x18\x01 \x01(\v2\x14.wallet.PixKeyPolicyR\n" +
	"policyNameBIZGgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/requestb\x06proto3"

var (
	file_services_request_pixDictManagementServiceRequest_proto_rawDescOnce sync.Once
	file_services_request_pixDictManagementServiceRequest_proto_rawDescData []byte
)

func file_services_request_pixDictManagementServiceRequest_proto_rawDescGZIP() []byte {
	file_services_request_pixDictManagementServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_request_pixDictManagementServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_request_pixDictManagementServiceRequest_proto_rawDesc), len(file_services_request_pixDictManagementServiceRequest_proto_rawDesc)))
	})
	return file_services_request_pixDictManagementServiceRequest_proto_rawDescData
}

var file_services_request_pixDictManagementServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_request_pixDictManagementServiceRequest_proto_goTypes = []any{
	(*PixKeyPolicyRequest)(nil),       // 0: request.PixKeyPolicyRequest
	(*PixKeyPolicyStatusRequest)(nil), // 1: request.PixKeyPolicyStatusRequest
	(*wallet.PixKeyPolicy)(nil),       // 2: wallet.PixKeyPolicy
}
var file_services_request_pixDictManagementServiceRequest_proto_depIdxs = []int32{
	2, // 0: request.PixKeyPolicyStatusRequest.policy_name:type_name -> wallet.PixKeyPolicy
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_services_request_pixDictManagementServiceRequest_proto_init() }
func file_services_request_pixDictManagementServiceRequest_proto_init() {
	if File_services_request_pixDictManagementServiceRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_request_pixDictManagementServiceRequest_proto_rawDesc), len(file_services_request_pixDictManagementServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_request_pixDictManagementServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_request_pixDictManagementServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_request_pixDictManagementServiceRequest_proto_msgTypes,
	}.Build()
	File_services_request_pixDictManagementServiceRequest_proto = out.File
	file_services_request_pixDictManagementServiceRequest_proto_goTypes = nil
	file_services_request_pixDictManagementServiceRequest_proto_depIdxs = nil
}