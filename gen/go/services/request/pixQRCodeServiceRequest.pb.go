// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/request/pixQRCodeServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QRCodeStaticCreateRequest struct {
	state           protoimpl.MessageState    `protogen:"open.v1"`
	RequestId       string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	TxId            string                    `protobuf:"bytes,2,opt,name=txId,proto3" json:"txId,omitempty"`
	Ispb            int32                     `protobuf:"varint,3,opt,name=ispb,proto3" json:"ispb,omitempty"`
	Format          wallet.QRCodeResponseType `protobuf:"varint,4,opt,name=format,proto3,enum=wallet.QRCodeResponseType" json:"format,omitempty"`
	Amount          float64                   `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`
	PixKey          *wallet.PixKey            `protobuf:"bytes,6,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	PixKeyOwnerName string                    `protobuf:"bytes,7,opt,name=pix_key_owner_name,json=pixKeyOwnerName,proto3" json:"pix_key_owner_name,omitempty"`
	PixKeyCity      string                    `protobuf:"bytes,8,opt,name=pix_key_city,json=pixKeyCity,proto3" json:"pix_key_city,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *QRCodeStaticCreateRequest) Reset() {
	*x = QRCodeStaticCreateRequest{}
	mi := &file_services_request_pixQRCodeServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QRCodeStaticCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QRCodeStaticCreateRequest) ProtoMessage() {}

func (x *QRCodeStaticCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixQRCodeServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QRCodeStaticCreateRequest.ProtoReflect.Descriptor instead.
func (*QRCodeStaticCreateRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixQRCodeServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *QRCodeStaticCreateRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *QRCodeStaticCreateRequest) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *QRCodeStaticCreateRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *QRCodeStaticCreateRequest) GetFormat() wallet.QRCodeResponseType {
	if x != nil {
		return x.Format
	}
	return wallet.QRCodeResponseType(0)
}

func (x *QRCodeStaticCreateRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *QRCodeStaticCreateRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *QRCodeStaticCreateRequest) GetPixKeyOwnerName() string {
	if x != nil {
		return x.PixKeyOwnerName
	}
	return ""
}

func (x *QRCodeStaticCreateRequest) GetPixKeyCity() string {
	if x != nil {
		return x.PixKeyCity
	}
	return ""
}

type QRCodeStaticDecodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	QrcodePayload string                 `protobuf:"bytes,2,opt,name=qrcode_payload,json=qrcodePayload,proto3" json:"qrcode_payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QRCodeStaticDecodeRequest) Reset() {
	*x = QRCodeStaticDecodeRequest{}
	mi := &file_services_request_pixQRCodeServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QRCodeStaticDecodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QRCodeStaticDecodeRequest) ProtoMessage() {}

func (x *QRCodeStaticDecodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_request_pixQRCodeServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QRCodeStaticDecodeRequest.ProtoReflect.Descriptor instead.
func (*QRCodeStaticDecodeRequest) Descriptor() ([]byte, []int) {
	return file_services_request_pixQRCodeServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *QRCodeStaticDecodeRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *QRCodeStaticDecodeRequest) GetQrcodePayload() string {
	if x != nil {
		return x.QrcodePayload
	}
	return ""
}

var File_services_request_pixQRCodeServiceRequest_proto protoreflect.FileDescriptor

const file_services_request_pixQRCodeServiceRequest_proto_rawDesc = "" +
	"\n" +
	".services/request/pixQRCodeServiceRequest.proto\x12\arequest\x1a\x13wallet/pixKey.proto\x1a\x16wallet/pixTransaction.proto\"\xa5\x02\n" +
	"\x19QRCodeStaticCreateRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04txId\x18\x02 \x01(\tR\x04txId\x12\x12\n" +
	"\x04ispb\x18\x03 \x01(\x05R\x04ispb\x122\n" +
	"\x06format\x18\x04 \x01(\x0e2\x1a.wallet.QRCodeResponseTypeR\x06format\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\x01R\x06amount\x12'\n" +
	"\apix_key\x18\x06 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12+\n" +
	"\x12pix_key_owner_name\x18\a \x01(\tR\x0fpixKeyOwnerName\x12 \n" +
	"\fpix_key_city\x18\b \x01(\tR\n" +
	"pixKeyCity\"`\n" +
	"\x19QRCodeStaticDecodeRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12%\n" +
	"\x0eqrcode_payload\x18\x02 \x01(\tR\rqrcodePayloadBIZGgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/requestb\x06proto3"

var (
	file_services_request_pixQRCodeServiceRequest_proto_rawDescOnce sync.Once
	file_services_request_pixQRCodeServiceRequest_proto_rawDescData []byte
)

func file_services_request_pixQRCodeServiceRequest_proto_rawDescGZIP() []byte {
	file_services_request_pixQRCodeServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_request_pixQRCodeServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_request_pixQRCodeServiceRequest_proto_rawDesc), len(file_services_request_pixQRCodeServiceRequest_proto_rawDesc)))
	})
	return file_services_request_pixQRCodeServiceRequest_proto_rawDescData
}

var file_services_request_pixQRCodeServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_request_pixQRCodeServiceRequest_proto_goTypes = []any{
	(*QRCodeStaticCreateRequest)(nil), // 0: request.QRCodeStaticCreateRequest
	(*QRCodeStaticDecodeRequest)(nil), // 1: request.QRCodeStaticDecodeRequest
	(wallet.QRCodeResponseType)(0),    // 2: wallet.QRCodeResponseType
	(*wallet.PixKey)(nil),             // 3: wallet.PixKey
}
var file_services_request_pixQRCodeServiceRequest_proto_depIdxs = []int32{
	2, // 0: request.QRCodeStaticCreateRequest.format:type_name -> wallet.QRCodeResponseType
	3, // 1: request.QRCodeStaticCreateRequest.pix_key:type_name -> wallet.PixKey
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_services_request_pixQRCodeServiceRequest_proto_init() }
func file_services_request_pixQRCodeServiceRequest_proto_init() {
	if File_services_request_pixQRCodeServiceRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_request_pixQRCodeServiceRequest_proto_rawDesc), len(file_services_request_pixQRCodeServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_request_pixQRCodeServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_request_pixQRCodeServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_request_pixQRCodeServiceRequest_proto_msgTypes,
	}.Build()
	File_services_request_pixQRCodeServiceRequest_proto = out.File
	file_services_request_pixQRCodeServiceRequest_proto_goTypes = nil
	file_services_request_pixQRCodeServiceRequest_proto_depIdxs = nil
}