// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pixDictService.proto

package services

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixDictService_PixKeyCreate_FullMethodName         = "/services.PixDictService/PixKeyCreate"
	PixDictService_PixKeyDelete_FullMethodName         = "/services.PixDictService/PixKeyDelete"
	PixDictService_PixKeyUpdate_FullMethodName         = "/services.PixDictService/PixKeyUpdate"
	PixDictService_PixKeyListByAccount_FullMethodName  = "/services.PixDictService/PixKeyListByAccount"
	PixDictService_PixKeyIsExist_FullMethodName        = "/services.PixDictService/PixKeyIsExist"
	PixDictService_PixKeyGet_FullMethodName            = "/services.PixDictService/PixKeyGet"
	PixDictService_NotifyAccountClosure_FullMethodName = "/services.PixDictService/NotifyAccountClosure"
	PixDictService_PixKeyClaim_FullMethodName          = "/services.PixDictService/PixKeyClaim"
)

// PixDictServiceClient is the client API for PixDictService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixDictServiceClient interface {
	PixKeyCreate(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
	PixKeyDelete(ctx context.Context, in *request.PixKeyDeleteRequest, opts ...grpc.CallOption) (*response.PixKeyDeleteResponse, error)
	PixKeyUpdate(ctx context.Context, in *request.PixKeyUpdateRequest, opts ...grpc.CallOption) (*response.PixKeyUpdateResponse, error)
	PixKeyListByAccount(ctx context.Context, in *request.PixKeyListByAccountRequest, opts ...grpc.CallOption) (*response.PixKeyListByAccountResponse, error)
	PixKeyIsExist(ctx context.Context, in *request.PixKeyIsExistRequest, opts ...grpc.CallOption) (*response.PixKeyIsExistResponse, error)
	// Used as first step when initiating a payment.
	PixKeyGet(ctx context.Context, in *request.PixKeyGetRequest, opts ...grpc.CallOption) (*response.PixKeyGetResponse, error)
	NotifyAccountClosure(ctx context.Context, in *request.NotifyAccountClosureRequest, opts ...grpc.CallOption) (*response.NotifyAccountClosureResponse, error)
	PixKeyClaim(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
}

type pixDictServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixDictServiceClient(cc grpc.ClientConnInterface) PixDictServiceClient {
	return &pixDictServiceClient{cc}
}

func (c *pixDictServiceClient) PixKeyCreate(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyDelete(ctx context.Context, in *request.PixKeyDeleteRequest, opts ...grpc.CallOption) (*response.PixKeyDeleteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyDeleteResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyUpdate(ctx context.Context, in *request.PixKeyUpdateRequest, opts ...grpc.CallOption) (*response.PixKeyUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyUpdateResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyListByAccount(ctx context.Context, in *request.PixKeyListByAccountRequest, opts ...grpc.CallOption) (*response.PixKeyListByAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyListByAccountResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyListByAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyIsExist(ctx context.Context, in *request.PixKeyIsExistRequest, opts ...grpc.CallOption) (*response.PixKeyIsExistResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyIsExistResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyIsExist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyGet(ctx context.Context, in *request.PixKeyGetRequest, opts ...grpc.CallOption) (*response.PixKeyGetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyGetResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) NotifyAccountClosure(ctx context.Context, in *request.NotifyAccountClosureRequest, opts ...grpc.CallOption) (*response.NotifyAccountClosureResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.NotifyAccountClosureResponse)
	err := c.cc.Invoke(ctx, PixDictService_NotifyAccountClosure_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyClaim(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyClaim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixDictServiceServer is the server API for PixDictService service.
// All implementations must embed UnimplementedPixDictServiceServer
// for forward compatibility.
type PixDictServiceServer interface {
	PixKeyCreate(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	PixKeyDelete(context.Context, *request.PixKeyDeleteRequest) (*response.PixKeyDeleteResponse, error)
	PixKeyUpdate(context.Context, *request.PixKeyUpdateRequest) (*response.PixKeyUpdateResponse, error)
	PixKeyListByAccount(context.Context, *request.PixKeyListByAccountRequest) (*response.PixKeyListByAccountResponse, error)
	PixKeyIsExist(context.Context, *request.PixKeyIsExistRequest) (*response.PixKeyIsExistResponse, error)
	// Used as first step when initiating a payment.
	PixKeyGet(context.Context, *request.PixKeyGetRequest) (*response.PixKeyGetResponse, error)
	NotifyAccountClosure(context.Context, *request.NotifyAccountClosureRequest) (*response.NotifyAccountClosureResponse, error)
	PixKeyClaim(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error)
	mustEmbedUnimplementedPixDictServiceServer()
}

// UnimplementedPixDictServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixDictServiceServer struct{}

func (UnimplementedPixDictServiceServer) PixKeyCreate(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyCreate not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyDelete(context.Context, *request.PixKeyDeleteRequest) (*response.PixKeyDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyDelete not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyUpdate(context.Context, *request.PixKeyUpdateRequest) (*response.PixKeyUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyUpdate not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyListByAccount(context.Context, *request.PixKeyListByAccountRequest) (*response.PixKeyListByAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyListByAccount not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyIsExist(context.Context, *request.PixKeyIsExistRequest) (*response.PixKeyIsExistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyIsExist not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyGet(context.Context, *request.PixKeyGetRequest) (*response.PixKeyGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyGet not implemented")
}
func (UnimplementedPixDictServiceServer) NotifyAccountClosure(context.Context, *request.NotifyAccountClosureRequest) (*response.NotifyAccountClosureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyAccountClosure not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyClaim(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaim not implemented")
}
func (UnimplementedPixDictServiceServer) mustEmbedUnimplementedPixDictServiceServer() {}
func (UnimplementedPixDictServiceServer) testEmbeddedByValue()                        {}

// UnsafePixDictServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixDictServiceServer will
// result in compilation errors.
type UnsafePixDictServiceServer interface {
	mustEmbedUnimplementedPixDictServiceServer()
}

func RegisterPixDictServiceServer(s grpc.ServiceRegistrar, srv PixDictServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixDictServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixDictService_ServiceDesc, srv)
}

func _PixDictService_PixKeyCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyCreate(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyDelete(ctx, req.(*request.PixKeyDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyUpdate(ctx, req.(*request.PixKeyUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyListByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyListByAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyListByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyListByAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyListByAccount(ctx, req.(*request.PixKeyListByAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyIsExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyIsExist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyIsExist(ctx, req.(*request.PixKeyIsExistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyGet(ctx, req.(*request.PixKeyGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_NotifyAccountClosure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.NotifyAccountClosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).NotifyAccountClosure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_NotifyAccountClosure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).NotifyAccountClosure(ctx, req.(*request.NotifyAccountClosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyClaim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyClaim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyClaim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyClaim(ctx, req.(*request.PixKeyClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixDictService_ServiceDesc is the grpc.ServiceDesc for PixDictService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixDictService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixDictService",
	HandlerType: (*PixDictServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixKeyCreate",
			Handler:    _PixDictService_PixKeyCreate_Handler,
		},
		{
			MethodName: "PixKeyDelete",
			Handler:    _PixDictService_PixKeyDelete_Handler,
		},
		{
			MethodName: "PixKeyUpdate",
			Handler:    _PixDictService_PixKeyUpdate_Handler,
		},
		{
			MethodName: "PixKeyListByAccount",
			Handler:    _PixDictService_PixKeyListByAccount_Handler,
		},
		{
			MethodName: "PixKeyIsExist",
			Handler:    _PixDictService_PixKeyIsExist_Handler,
		},
		{
			MethodName: "PixKeyGet",
			Handler:    _PixDictService_PixKeyGet_Handler,
		},
		{
			MethodName: "NotifyAccountClosure",
			Handler:    _PixDictService_NotifyAccountClosure_Handler,
		},
		{
			MethodName: "PixKeyClaim",
			Handler:    _PixDictService_PixKeyClaim_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pixDictService.proto",
}
