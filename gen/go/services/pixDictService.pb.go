// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pixDictService.proto

package services

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pixDictService_proto protoreflect.FileDescriptor

const file_services_pixDictService_proto_rawDesc = "" +
	"\n" +
	"\x1dservices/pixDictService.proto\x12\bservices\x1a,services/request/pixDictServiceRequest.proto\x1a.services/response/pixDictServiceResponse.proto2\xa4\x05\n" +
	"\x0ePixDictService\x12L\n" +
	"\fPixKeyCreate\x12\x1c.request.PixKeyCreateRequest\x1a\x1e.response.PixKeyCreateResponse\x12L\n" +
	"\fPixKeyDelete\x12\x1c.request.PixKeyDeleteRequest\x1a\x1e.response.PixKeyDeleteResponse\x12L\n" +
	"\fPixKeyUpdate\x12\x1c.request.PixKeyUpdateRequest\x1a\x1e.response.PixKeyUpdateResponse\x12a\n" +
	"\x13PixKeyListByAccount\x12#.request.PixKeyListByAccountRequest\x1a%.response.PixKeyListByAccountResponse\x12O\n" +
	"\rPixKeyIsExist\x12\x1d.request.PixKeyIsExistRequest\x1a\x1f.response.PixKeyIsExistResponse\x12C\n" +
	"\tPixKeyGet\x12\x19.request.PixKeyGetRequest\x1a\x1b.response.PixKeyGetResponse\x12d\n" +
	"\x14NotifyAccountClosure\x12$.request.NotifyAccountClosureRequest\x1a&.response.NotifyAccountClosureResponse\x12I\n" +
	"\vPixKeyClaim\x12\x1b.request.PixKeyClaimRequest\x1a\x1d.response.PixKeyClaimResponseBAZ?gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/servicesb\x06proto3"

var file_services_pixDictService_proto_goTypes = []any{
	(*request.PixKeyCreateRequest)(nil),           // 0: request.PixKeyCreateRequest
	(*request.PixKeyDeleteRequest)(nil),           // 1: request.PixKeyDeleteRequest
	(*request.PixKeyUpdateRequest)(nil),           // 2: request.PixKeyUpdateRequest
	(*request.PixKeyListByAccountRequest)(nil),    // 3: request.PixKeyListByAccountRequest
	(*request.PixKeyIsExistRequest)(nil),          // 4: request.PixKeyIsExistRequest
	(*request.PixKeyGetRequest)(nil),              // 5: request.PixKeyGetRequest
	(*request.NotifyAccountClosureRequest)(nil),   // 6: request.NotifyAccountClosureRequest
	(*request.PixKeyClaimRequest)(nil),            // 7: request.PixKeyClaimRequest
	(*response.PixKeyCreateResponse)(nil),         // 8: response.PixKeyCreateResponse
	(*response.PixKeyDeleteResponse)(nil),         // 9: response.PixKeyDeleteResponse
	(*response.PixKeyUpdateResponse)(nil),         // 10: response.PixKeyUpdateResponse
	(*response.PixKeyListByAccountResponse)(nil),  // 11: response.PixKeyListByAccountResponse
	(*response.PixKeyIsExistResponse)(nil),        // 12: response.PixKeyIsExistResponse
	(*response.PixKeyGetResponse)(nil),            // 13: response.PixKeyGetResponse
	(*response.NotifyAccountClosureResponse)(nil), // 14: response.NotifyAccountClosureResponse
	(*response.PixKeyClaimResponse)(nil),          // 15: response.PixKeyClaimResponse
}
var file_services_pixDictService_proto_depIdxs = []int32{
	0,  // 0: services.PixDictService.PixKeyCreate:input_type -> request.PixKeyCreateRequest
	1,  // 1: services.PixDictService.PixKeyDelete:input_type -> request.PixKeyDeleteRequest
	2,  // 2: services.PixDictService.PixKeyUpdate:input_type -> request.PixKeyUpdateRequest
	3,  // 3: services.PixDictService.PixKeyListByAccount:input_type -> request.PixKeyListByAccountRequest
	4,  // 4: services.PixDictService.PixKeyIsExist:input_type -> request.PixKeyIsExistRequest
	5,  // 5: services.PixDictService.PixKeyGet:input_type -> request.PixKeyGetRequest
	6,  // 6: services.PixDictService.NotifyAccountClosure:input_type -> request.NotifyAccountClosureRequest
	7,  // 7: services.PixDictService.PixKeyClaim:input_type -> request.PixKeyClaimRequest
	8,  // 8: services.PixDictService.PixKeyCreate:output_type -> response.PixKeyCreateResponse
	9,  // 9: services.PixDictService.PixKeyDelete:output_type -> response.PixKeyDeleteResponse
	10, // 10: services.PixDictService.PixKeyUpdate:output_type -> response.PixKeyUpdateResponse
	11, // 11: services.PixDictService.PixKeyListByAccount:output_type -> response.PixKeyListByAccountResponse
	12, // 12: services.PixDictService.PixKeyIsExist:output_type -> response.PixKeyIsExistResponse
	13, // 13: services.PixDictService.PixKeyGet:output_type -> response.PixKeyGetResponse
	14, // 14: services.PixDictService.NotifyAccountClosure:output_type -> response.NotifyAccountClosureResponse
	15, // 15: services.PixDictService.PixKeyClaim:output_type -> response.PixKeyClaimResponse
	8,  // [8:16] is the sub-list for method output_type
	0,  // [0:8] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_services_pixDictService_proto_init() }
func file_services_pixDictService_proto_init() {
	if File_services_pixDictService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pixDictService_proto_rawDesc), len(file_services_pixDictService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pixDictService_proto_goTypes,
		DependencyIndexes: file_services_pixDictService_proto_depIdxs,
	}.Build()
	File_services_pixDictService_proto = out.File
	file_services_pixDictService_proto_goTypes = nil
	file_services_pixDictService_proto_depIdxs = nil
}
