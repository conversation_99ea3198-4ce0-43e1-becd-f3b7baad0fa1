// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/response/pixDictServiceResponse.proto

package response

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreatePixKeyResponse struct {
	state                      protoimpl.MessageState    `protogen:"open.v1"`
	Result                     wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	PixKey                     *wallet.PixKey            `protobuf:"bytes,2,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp    `protobuf:"bytes,3,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp    `protobuf:"bytes,4,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *CreatePixKeyResponse) Reset() {
	*x = CreatePixKeyResponse{}
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePixKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePixKeyResponse) ProtoMessage() {}

func (x *CreatePixKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePixKeyResponse.ProtoReflect.Descriptor instead.
func (*CreatePixKeyResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixKeyServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePixKeyResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *CreatePixKeyResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *CreatePixKeyResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *CreatePixKeyResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type DeletePixKeyResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	PixKey                     *wallet.PixKey         `protobuf:"bytes,1,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *DeletePixKeyResponse) Reset() {
	*x = DeletePixKeyResponse{}
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePixKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePixKeyResponse) ProtoMessage() {}

func (x *DeletePixKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePixKeyResponse.ProtoReflect.Descriptor instead.
func (*DeletePixKeyResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixKeyServiceResponse_proto_rawDescGZIP(), []int{1}
}

func (x *DeletePixKeyResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *DeletePixKeyResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *DeletePixKeyResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type UpdatePixKeyResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	Result                     wallet.CommonResult    `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	PixKey                     *wallet.PixKey         `protobuf:"bytes,2,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *UpdatePixKeyResponse) Reset() {
	*x = UpdatePixKeyResponse{}
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePixKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePixKeyResponse) ProtoMessage() {}

func (x *UpdatePixKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePixKeyResponse.ProtoReflect.Descriptor instead.
func (*UpdatePixKeyResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixKeyServiceResponse_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePixKeyResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *UpdatePixKeyResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *UpdatePixKeyResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *UpdatePixKeyResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type PixKeyListByAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        wallet.CommonResult    `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	PixKey        []*wallet.PixKey       `protobuf:"bytes,2,rep,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyListByAccountResponse) Reset() {
	*x = PixKeyListByAccountResponse{}
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyListByAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyListByAccountResponse) ProtoMessage() {}

func (x *PixKeyListByAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyListByAccountResponse.ProtoReflect.Descriptor instead.
func (*PixKeyListByAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixKeyServiceResponse_proto_rawDescGZIP(), []int{3}
}

func (x *PixKeyListByAccountResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *PixKeyListByAccountResponse) GetPixKey() []*wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

type PixKeyClaimResponse struct {
	state                protoimpl.MessageState    `protogen:"open.v1"`
	Result               wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	IspbDonator          int32                     `protobuf:"varint,2,opt,name=ispb_donator,json=ispbDonator,proto3" json:"ispb_donator,omitempty"`
	ClaimId              string                    `protobuf:"bytes,3,opt,name=claim_id,json=claimId,proto3" json:"claim_id,omitempty"`
	ClaimStatus          wallet.PixKeyClaimStatus  `protobuf:"varint,4,opt,name=claim_status,json=claimStatus,proto3,enum=wallet.PixKeyClaimStatus" json:"claim_status,omitempty"`
	ClaimResolutionLimit *timestamppb.Timestamp    `protobuf:"bytes,5,opt,name=claim_resolution_limit,json=claimResolutionLimit,proto3" json:"claim_resolution_limit,omitempty"` // Claim Resolution Limit Date and Time for donator
	ClaimConclusionLimit *timestamppb.Timestamp    `protobuf:"bytes,6,opt,name=claim_conclusion_limit,json=claimConclusionLimit,proto3" json:"claim_conclusion_limit,omitempty"` // Claim Limit for requester to confirm the claim
	ClaimLastModifiedAt  *timestamppb.Timestamp    `protobuf:"bytes,7,opt,name=claim_last_modified_at,json=claimLastModifiedAt,proto3" json:"claim_last_modified_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PixKeyClaimResponse) Reset() {
	*x = PixKeyClaimResponse{}
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimResponse) ProtoMessage() {}

func (x *PixKeyClaimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixKeyServiceResponse_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimResponse.ProtoReflect.Descriptor instead.
func (*PixKeyClaimResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixKeyServiceResponse_proto_rawDescGZIP(), []int{4}
}

func (x *PixKeyClaimResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyClaimResponse) GetIspbDonator() int32 {
	if x != nil {
		return x.IspbDonator
	}
	return 0
}

func (x *PixKeyClaimResponse) GetClaimId() string {
	if x != nil {
		return x.ClaimId
	}
	return ""
}

func (x *PixKeyClaimResponse) GetClaimStatus() wallet.PixKeyClaimStatus {
	if x != nil {
		return x.ClaimStatus
	}
	return wallet.PixKeyClaimStatus(0)
}

func (x *PixKeyClaimResponse) GetClaimResolutionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimResolutionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimConclusionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimConclusionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimLastModifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimLastModifiedAt
	}
	return nil
}

var File_services_response_pixKeyServiceResponse_proto protoreflect.FileDescriptor

const file_services_response_pixKeyServiceResponse_proto_rawDesc = "" +
	"\n" +
	"-services/response/pixDictServiceResponse.proto\x12\bresponse\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x13wallet/pixKey.proto\"\xa2\x02\n" +
	"\x14CreatePixKeyResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"\xee\x01\n" +
	"\x14DeletePixKeyResponse\x12'\n" +
	"\apix_key\x18\x01 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"\x9c\x02\n" +
	"\x14UpdatePixKeyResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"t\n" +
	"\x1bPixKeyListByAccountResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x03(\v2\x0e.wallet.PixKeyR\x06pixKey\"\xba\x03\n" +
	"\x13PixKeyClaimResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12!\n" +
	"\fispb_donator\x18\x02 \x01(\x05R\vispbDonator\x12\x19\n" +
	"\bclaim_id\x18\x03 \x01(\tR\aclaimId\x12<\n" +
	"\fclaim_status\x18\x04 \x01(\x0e2\x19.wallet.PixKeyClaimStatusR\vclaimStatus\x12P\n" +
	"\x16claim_resolution_limit\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimResolutionLimit\x12P\n" +
	"\x16claim_conclusion_limit\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimConclusionLimit\x12O\n" +
	"\x16claim_last_modified_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x13claimLastModifiedAtBJZHgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/responseb\x06proto3"

var (
	file_services_response_pixKeyServiceResponse_proto_rawDescOnce sync.Once
	file_services_response_pixKeyServiceResponse_proto_rawDescData []byte
)

func file_services_response_pixKeyServiceResponse_proto_rawDescGZIP() []byte {
	file_services_response_pixKeyServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_response_pixKeyServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_response_pixKeyServiceResponse_proto_rawDesc), len(file_services_response_pixKeyServiceResponse_proto_rawDesc)))
	})
	return file_services_response_pixKeyServiceResponse_proto_rawDescData
}

var file_services_response_pixKeyServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_services_response_pixKeyServiceResponse_proto_goTypes = []any{
	(*CreatePixKeyResponse)(nil),        // 0: response.CreatePixKeyResponse
	(*DeletePixKeyResponse)(nil),        // 1: response.DeletePixKeyResponse
	(*UpdatePixKeyResponse)(nil),        // 2: response.UpdatePixKeyResponse
	(*PixKeyListByAccountResponse)(nil), // 3: response.PixKeyListByAccountResponse
	(*PixKeyClaimResponse)(nil),         // 4: response.PixKeyClaimResponse
	(wallet.PixKeyCreateResult)(0),      // 5: wallet.PixKeyCreateResult
	(*wallet.PixKey)(nil),               // 6: wallet.PixKey
	(*timestamppb.Timestamp)(nil),       // 7: google.protobuf.Timestamp
	(wallet.CommonResult)(0),            // 8: wallet.CommonResult
	(wallet.PixKeyClaimStatus)(0),       // 9: wallet.PixKeyClaimStatus
}
var file_services_response_pixKeyServiceResponse_proto_depIdxs = []int32{
	5,  // 0: response.CreatePixKeyResponse.result:type_name -> wallet.PixKeyCreateResult
	6,  // 1: response.CreatePixKeyResponse.pix_key:type_name -> wallet.PixKey
	7,  // 2: response.CreatePixKeyResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	7,  // 3: response.CreatePixKeyResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	6,  // 4: response.DeletePixKeyResponse.pix_key:type_name -> wallet.PixKey
	7,  // 5: response.DeletePixKeyResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	7,  // 6: response.DeletePixKeyResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	8,  // 7: response.UpdatePixKeyResponse.result:type_name -> wallet.CommonResult
	6,  // 8: response.UpdatePixKeyResponse.pix_key:type_name -> wallet.PixKey
	7,  // 9: response.UpdatePixKeyResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	7,  // 10: response.UpdatePixKeyResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	8,  // 11: response.PixKeyListByAccountResponse.result:type_name -> wallet.CommonResult
	6,  // 12: response.PixKeyListByAccountResponse.pix_key:type_name -> wallet.PixKey
	5,  // 13: response.PixKeyClaimResponse.result:type_name -> wallet.PixKeyCreateResult
	9,  // 14: response.PixKeyClaimResponse.claim_status:type_name -> wallet.PixKeyClaimStatus
	7,  // 15: response.PixKeyClaimResponse.claim_resolution_limit:type_name -> google.protobuf.Timestamp
	7,  // 16: response.PixKeyClaimResponse.claim_conclusion_limit:type_name -> google.protobuf.Timestamp
	7,  // 17: response.PixKeyClaimResponse.claim_last_modified_at:type_name -> google.protobuf.Timestamp
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_services_response_pixKeyServiceResponse_proto_init() }
func file_services_response_pixKeyServiceResponse_proto_init() {
	if File_services_response_pixKeyServiceResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_response_pixKeyServiceResponse_proto_rawDesc), len(file_services_response_pixKeyServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_response_pixKeyServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_response_pixKeyServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_response_pixKeyServiceResponse_proto_msgTypes,
	}.Build()
	File_services_response_pixKeyServiceResponse_proto = out.File
	file_services_response_pixKeyServiceResponse_proto_goTypes = nil
	file_services_response_pixKeyServiceResponse_proto_depIdxs = nil
}