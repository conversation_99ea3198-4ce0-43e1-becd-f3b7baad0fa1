// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/response/pixParticipantServiceResponse.proto

package response

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyPolicyResponse struct {
	state          protoimpl.MessageState    `protogen:"open.v1"`
	Result         wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	BucketCategory string                    `protobuf:"bytes,2,opt,name=bucket_category,json=bucketCategory,proto3" json:"bucket_category,omitempty"`
	Policies       []*wallet.PixKeyPolicy    `protobuf:"bytes,3,rep,name=policies,proto3" json:"policies,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixKeyPolicyResponse) Reset() {
	*x = PixKeyPolicyResponse{}
	mi := &file_services_response_pixDictManagementServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyResponse) ProtoMessage() {}

func (x *PixKeyPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictManagementServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyResponse.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictManagementServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyPolicyResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyPolicyResponse) GetBucketCategory() string {
	if x != nil {
		return x.BucketCategory
	}
	return ""
}

func (x *PixKeyPolicyResponse) GetPolicies() []*wallet.PixKeyPolicy {
	if x != nil {
		return x.Policies
	}
	return nil
}

type PixKeyPolicyStatusResponse struct {
	state          protoimpl.MessageState    `protogen:"open.v1"`
	Result         wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	BucketCategory string                    `protobuf:"bytes,2,opt,name=bucket_category,json=bucketCategory,proto3" json:"bucket_category,omitempty"`
	Policy         *wallet.PixKeyPolicy      `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixKeyPolicyStatusResponse) Reset() {
	*x = PixKeyPolicyStatusResponse{}
	mi := &file_services_response_pixDictManagementServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyStatusResponse) ProtoMessage() {}

func (x *PixKeyPolicyStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictManagementServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyStatusResponse.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyStatusResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictManagementServiceResponse_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyPolicyStatusResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyPolicyStatusResponse) GetBucketCategory() string {
	if x != nil {
		return x.BucketCategory
	}
	return ""
}

func (x *PixKeyPolicyStatusResponse) GetPolicy() *wallet.PixKeyPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

var File_services_response_pixDictManagementServiceResponse_proto protoreflect.FileDescriptor

const file_services_response_pixDictManagementServiceResponse_proto_rawDesc = "" +
	"\n" +
	"8services/response/pixParticipantServiceResponse.proto\x12\bresponse\x1a\x13wallet/pixKey.proto\"\xa5\x01\n" +
	"\x14PixKeyPolicyResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12'\n" +
	"\x0fbucket_category\x18\x02 \x01(\tR\x0ebucketCategory\x120\n" +
	"\bpolicies\x18\x03 \x03(\v2\x14.wallet.PixKeyPolicyR\bpolicies\"\xa7\x01\n" +
	"\x1aPixKeyPolicyStatusResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12'\n" +
	"\x0fbucket_category\x18\x02 \x01(\tR\x0ebucketCategory\x12,\n" +
	"\x06policy\x18\x03 \x01(\v2\x14.wallet.PixKeyPolicyR\x06policyBJZHgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/responseb\x06proto3"

var (
	file_services_response_pixDictManagementServiceResponse_proto_rawDescOnce sync.Once
	file_services_response_pixDictManagementServiceResponse_proto_rawDescData []byte
)

func file_services_response_pixDictManagementServiceResponse_proto_rawDescGZIP() []byte {
	file_services_response_pixDictManagementServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_response_pixDictManagementServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_response_pixDictManagementServiceResponse_proto_rawDesc), len(file_services_response_pixDictManagementServiceResponse_proto_rawDesc)))
	})
	return file_services_response_pixDictManagementServiceResponse_proto_rawDescData
}

var file_services_response_pixDictManagementServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_response_pixDictManagementServiceResponse_proto_goTypes = []any{
	(*PixKeyPolicyResponse)(nil),       // 0: response.PixKeyPolicyResponse
	(*PixKeyPolicyStatusResponse)(nil), // 1: response.PixKeyPolicyStatusResponse
	(wallet.PixKeyCreateResult)(0),     // 2: wallet.PixKeyCreateResult
	(*wallet.PixKeyPolicy)(nil),        // 3: wallet.PixKeyPolicy
}
var file_services_response_pixDictManagementServiceResponse_proto_depIdxs = []int32{
	2, // 0: response.PixKeyPolicyResponse.result:type_name -> wallet.PixKeyCreateResult
	3, // 1: response.PixKeyPolicyResponse.policies:type_name -> wallet.PixKeyPolicy
	2, // 2: response.PixKeyPolicyStatusResponse.result:type_name -> wallet.PixKeyCreateResult
	3, // 3: response.PixKeyPolicyStatusResponse.policy:type_name -> wallet.PixKeyPolicy
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_services_response_pixDictManagementServiceResponse_proto_init() }
func file_services_response_pixDictManagementServiceResponse_proto_init() {
	if File_services_response_pixDictManagementServiceResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_response_pixDictManagementServiceResponse_proto_rawDesc), len(file_services_response_pixDictManagementServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_response_pixDictManagementServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_response_pixDictManagementServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_response_pixDictManagementServiceResponse_proto_msgTypes,
	}.Build()
	File_services_response_pixDictManagementServiceResponse_proto = out.File
	file_services_response_pixDictManagementServiceResponse_proto_goTypes = nil
	file_services_response_pixDictManagementServiceResponse_proto_depIdxs = nil
}