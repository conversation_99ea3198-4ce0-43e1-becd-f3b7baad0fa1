// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/response/pixQRCodeServiceResponse.proto

package response

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QRCodeStaticCreateResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	QrcodeImageBase64   string                 `protobuf:"bytes,1,opt,name=qrcode_image_base64,json=qrcodeImageBase64,proto3" json:"qrcode_image_base64,omitempty"`
	QrcodePayloadBase64 string                 `protobuf:"bytes,2,opt,name=qrcode_payload_base64,json=qrcodePayloadBase64,proto3" json:"qrcode_payload_base64,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *QRCodeStaticCreateResponse) Reset() {
	*x = QRCodeStaticCreateResponse{}
	mi := &file_services_response_pixQRCodeServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QRCodeStaticCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QRCodeStaticCreateResponse) ProtoMessage() {}

func (x *QRCodeStaticCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixQRCodeServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QRCodeStaticCreateResponse.ProtoReflect.Descriptor instead.
func (*QRCodeStaticCreateResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixQRCodeServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *QRCodeStaticCreateResponse) GetQrcodeImageBase64() string {
	if x != nil {
		return x.QrcodeImageBase64
	}
	return ""
}

func (x *QRCodeStaticCreateResponse) GetQrcodePayloadBase64() string {
	if x != nil {
		return x.QrcodePayloadBase64
	}
	return ""
}

type QRCodeStaticDecodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QRCodeStaticDecodeResponse) Reset() {
	*x = QRCodeStaticDecodeResponse{}
	mi := &file_services_response_pixQRCodeServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QRCodeStaticDecodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QRCodeStaticDecodeResponse) ProtoMessage() {}

func (x *QRCodeStaticDecodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixQRCodeServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QRCodeStaticDecodeResponse.ProtoReflect.Descriptor instead.
func (*QRCodeStaticDecodeResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixQRCodeServiceResponse_proto_rawDescGZIP(), []int{1}
}

var File_services_response_pixQRCodeServiceResponse_proto protoreflect.FileDescriptor

const file_services_response_pixQRCodeServiceResponse_proto_rawDesc = "" +
	"\n" +
	"0services/response/pixQRCodeServiceResponse.proto\x12\bresponse\"\x80\x01\n" +
	"\x1aQRCodeStaticCreateResponse\x12.\n" +
	"\x13qrcode_image_base64\x18\x01 \x01(\tR\x11qrcodeImageBase64\x122\n" +
	"\x15qrcode_payload_base64\x18\x02 \x01(\tR\x13qrcodePayloadBase64\"\x1c\n" +
	"\x1aQRCodeStaticDecodeResponseBJZHgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/responseb\x06proto3"

var (
	file_services_response_pixQRCodeServiceResponse_proto_rawDescOnce sync.Once
	file_services_response_pixQRCodeServiceResponse_proto_rawDescData []byte
)

func file_services_response_pixQRCodeServiceResponse_proto_rawDescGZIP() []byte {
	file_services_response_pixQRCodeServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_response_pixQRCodeServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_response_pixQRCodeServiceResponse_proto_rawDesc), len(file_services_response_pixQRCodeServiceResponse_proto_rawDesc)))
	})
	return file_services_response_pixQRCodeServiceResponse_proto_rawDescData
}

var file_services_response_pixQRCodeServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_response_pixQRCodeServiceResponse_proto_goTypes = []any{
	(*QRCodeStaticCreateResponse)(nil), // 0: response.QRCodeStaticCreateResponse
	(*QRCodeStaticDecodeResponse)(nil), // 1: response.QRCodeStaticDecodeResponse
}
var file_services_response_pixQRCodeServiceResponse_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_response_pixQRCodeServiceResponse_proto_init() }
func file_services_response_pixQRCodeServiceResponse_proto_init() {
	if File_services_response_pixQRCodeServiceResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_response_pixQRCodeServiceResponse_proto_rawDesc), len(file_services_response_pixQRCodeServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_response_pixQRCodeServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_response_pixQRCodeServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_response_pixQRCodeServiceResponse_proto_msgTypes,
	}.Build()
	File_services_response_pixQRCodeServiceResponse_proto = out.File
	file_services_response_pixQRCodeServiceResponse_proto_goTypes = nil
	file_services_response_pixQRCodeServiceResponse_proto_depIdxs = nil
}
