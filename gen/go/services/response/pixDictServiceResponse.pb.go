// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/response/pixDictServiceResponse.proto

package response

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyCreateResponse struct {
	state                      protoimpl.MessageState    `protogen:"open.v1"`
	Result                     wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	PixKey                     *wallet.PixKey            `protobuf:"bytes,2,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp    `protobuf:"bytes,3,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp    `protobuf:"bytes,4,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *PixKeyCreateResponse) Reset() {
	*x = PixKeyCreateResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyCreateResponse) ProtoMessage() {}

func (x *PixKeyCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyCreateResponse.ProtoReflect.Descriptor instead.
func (*PixKeyCreateResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyCreateResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyCreateResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyCreateResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *PixKeyCreateResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type PixKeyDeleteResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	PixKey                     *wallet.PixKey         `protobuf:"bytes,1,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *PixKeyDeleteResponse) Reset() {
	*x = PixKeyDeleteResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyDeleteResponse) ProtoMessage() {}

func (x *PixKeyDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyDeleteResponse.ProtoReflect.Descriptor instead.
func (*PixKeyDeleteResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyDeleteResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyDeleteResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *PixKeyDeleteResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type PixKeyUpdateResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	Result                     wallet.CommonResult    `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	PixKey                     *wallet.PixKey         `protobuf:"bytes,2,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`                                                                 // The value of the Pix key (e.g., email, phone number, CPF/CNPJ, random key).
	KeyCreationDatetime        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=key_creation_datetime,json=keyCreationDatetime,proto3" json:"key_creation_datetime,omitempty"`                        // The date and time when the Pix key was created.
	KeyPossessionStartDatetime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=key_possession_start_datetime,json=keyPossessionStartDatetime,proto3" json:"key_possession_start_datetime,omitempty"` // The date and time when the possession of the Pix key started.
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *PixKeyUpdateResponse) Reset() {
	*x = PixKeyUpdateResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyUpdateResponse) ProtoMessage() {}

func (x *PixKeyUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyUpdateResponse.ProtoReflect.Descriptor instead.
func (*PixKeyUpdateResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{2}
}

func (x *PixKeyUpdateResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *PixKeyUpdateResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyUpdateResponse) GetKeyCreationDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyCreationDatetime
	}
	return nil
}

func (x *PixKeyUpdateResponse) GetKeyPossessionStartDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeyPossessionStartDatetime
	}
	return nil
}

type PixKeyListByAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        wallet.CommonResult    `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	PixKey        []*wallet.PixKey       `protobuf:"bytes,2,rep,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyListByAccountResponse) Reset() {
	*x = PixKeyListByAccountResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyListByAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyListByAccountResponse) ProtoMessage() {}

func (x *PixKeyListByAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyListByAccountResponse.ProtoReflect.Descriptor instead.
func (*PixKeyListByAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{3}
}

func (x *PixKeyListByAccountResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *PixKeyListByAccountResponse) GetPixKey() []*wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

type PixKeyIsExistResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Result         wallet.CommonResult    `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	ExistentPixKey []*wallet.PixKey       `protobuf:"bytes,2,rep,name=existent_pix_key,json=existentPixKey,proto3" json:"existent_pix_key,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixKeyIsExistResponse) Reset() {
	*x = PixKeyIsExistResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyIsExistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyIsExistResponse) ProtoMessage() {}

func (x *PixKeyIsExistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyIsExistResponse.ProtoReflect.Descriptor instead.
func (*PixKeyIsExistResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{4}
}

func (x *PixKeyIsExistResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *PixKeyIsExistResponse) GetExistentPixKey() []*wallet.PixKey {
	if x != nil {
		return x.ExistentPixKey
	}
	return nil
}

type PixKeyGetResponse struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	Result            wallet.CommonResult       `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.CommonResult" json:"result,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,2,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,3,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,4,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyGetResponse) Reset() {
	*x = PixKeyGetResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyGetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyGetResponse) ProtoMessage() {}

func (x *PixKeyGetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyGetResponse.ProtoReflect.Descriptor instead.
func (*PixKeyGetResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{5}
}

func (x *PixKeyGetResponse) GetResult() wallet.CommonResult {
	if x != nil {
		return x.Result
	}
	return wallet.CommonResult(0)
}

func (x *PixKeyGetResponse) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyGetResponse) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyGetResponse) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

type NotifyAccountClosureResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PixKey        []*wallet.PixKey       `protobuf:"bytes,2,rep,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotifyAccountClosureResponse) Reset() {
	*x = NotifyAccountClosureResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotifyAccountClosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyAccountClosureResponse) ProtoMessage() {}

func (x *NotifyAccountClosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyAccountClosureResponse.ProtoReflect.Descriptor instead.
func (*NotifyAccountClosureResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{6}
}

func (x *NotifyAccountClosureResponse) GetPixKey() []*wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

type PixKeyClaimResponse struct {
	state                protoimpl.MessageState    `protogen:"open.v1"`
	Result               wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	IspbDonator          int32                     `protobuf:"varint,2,opt,name=ispb_donator,json=ispbDonator,proto3" json:"ispb_donator,omitempty"`
	ClaimId              string                    `protobuf:"bytes,3,opt,name=claim_id,json=claimId,proto3" json:"claim_id,omitempty"`
	ClaimStatus          wallet.PixKeyClaimStatus  `protobuf:"varint,4,opt,name=claim_status,json=claimStatus,proto3,enum=wallet.PixKeyClaimStatus" json:"claim_status,omitempty"`
	ClaimResolutionLimit *timestamppb.Timestamp    `protobuf:"bytes,5,opt,name=claim_resolution_limit,json=claimResolutionLimit,proto3" json:"claim_resolution_limit,omitempty"` // Claim Resolution Limit Date and Time for donator
	ClaimConclusionLimit *timestamppb.Timestamp    `protobuf:"bytes,6,opt,name=claim_conclusion_limit,json=claimConclusionLimit,proto3" json:"claim_conclusion_limit,omitempty"` // Claim Limit for requester to confirm the claim
	ClaimLastModifiedAt  *timestamppb.Timestamp    `protobuf:"bytes,7,opt,name=claim_last_modified_at,json=claimLastModifiedAt,proto3" json:"claim_last_modified_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PixKeyClaimResponse) Reset() {
	*x = PixKeyClaimResponse{}
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimResponse) ProtoMessage() {}

func (x *PixKeyClaimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_response_pixDictServiceResponse_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimResponse.ProtoReflect.Descriptor instead.
func (*PixKeyClaimResponse) Descriptor() ([]byte, []int) {
	return file_services_response_pixDictServiceResponse_proto_rawDescGZIP(), []int{7}
}

func (x *PixKeyClaimResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyClaimResponse) GetIspbDonator() int32 {
	if x != nil {
		return x.IspbDonator
	}
	return 0
}

func (x *PixKeyClaimResponse) GetClaimId() string {
	if x != nil {
		return x.ClaimId
	}
	return ""
}

func (x *PixKeyClaimResponse) GetClaimStatus() wallet.PixKeyClaimStatus {
	if x != nil {
		return x.ClaimStatus
	}
	return wallet.PixKeyClaimStatus(0)
}

func (x *PixKeyClaimResponse) GetClaimResolutionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimResolutionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimConclusionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimConclusionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimLastModifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimLastModifiedAt
	}
	return nil
}

var File_services_response_pixDictServiceResponse_proto protoreflect.FileDescriptor

const file_services_response_pixDictServiceResponse_proto_rawDesc = "" +
	"\n" +
	".services/response/pixDictServiceResponse.proto\x12\bresponse\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x13wallet/pixKey.proto\x1a\x14wallet/banking.proto\"\xa2\x02\n" +
	"\x14PixKeyCreateResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"\xee\x01\n" +
	"\x14PixKeyDeleteResponse\x12'\n" +
	"\apix_key\x18\x01 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"\x9c\x02\n" +
	"\x14PixKeyUpdateResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x12N\n" +
	"\x15key_creation_datetime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x13keyCreationDatetime\x12]\n" +
	"\x1dkey_possession_start_datetime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x1akeyPossessionStartDatetime\"t\n" +
	"\x1bPixKeyListByAccountResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x03(\v2\x0e.wallet.PixKeyR\x06pixKey\"\x7f\n" +
	"\x15PixKeyIsExistResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x128\n" +
	"\x10existent_pix_key\x18\x02 \x03(\v2\x0e.wallet.PixKeyR\x0eexistentPixKey\"\xed\x01\n" +
	"\x11PixKeyGetResponse\x12,\n" +
	"\x06result\x18\x01 \x01(\x0e2\x14.wallet.CommonResultR\x06result\x12'\n" +
	"\apix_key\x18\x02 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x03 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x04 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\"G\n" +
	"\x1cNotifyAccountClosureResponse\x12'\n" +
	"\apix_key\x18\x02 \x03(\v2\x0e.wallet.PixKeyR\x06pixKey\"\xba\x03\n" +
	"\x13PixKeyClaimResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12!\n" +
	"\fispb_donator\x18\x02 \x01(\x05R\vispbDonator\x12\x19\n" +
	"\bclaim_id\x18\x03 \x01(\tR\aclaimId\x12<\n" +
	"\fclaim_status\x18\x04 \x01(\x0e2\x19.wallet.PixKeyClaimStatusR\vclaimStatus\x12P\n" +
	"\x16claim_resolution_limit\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimResolutionLimit\x12P\n" +
	"\x16claim_conclusion_limit\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimConclusionLimit\x12O\n" +
	"\x16claim_last_modified_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x13claimLastModifiedAtBJZHgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/responseb\x06proto3"

var (
	file_services_response_pixDictServiceResponse_proto_rawDescOnce sync.Once
	file_services_response_pixDictServiceResponse_proto_rawDescData []byte
)

func file_services_response_pixDictServiceResponse_proto_rawDescGZIP() []byte {
	file_services_response_pixDictServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_response_pixDictServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_response_pixDictServiceResponse_proto_rawDesc), len(file_services_response_pixDictServiceResponse_proto_rawDesc)))
	})
	return file_services_response_pixDictServiceResponse_proto_rawDescData
}

var file_services_response_pixDictServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_services_response_pixDictServiceResponse_proto_goTypes = []any{
	(*PixKeyCreateResponse)(nil),         // 0: response.PixKeyCreateResponse
	(*PixKeyDeleteResponse)(nil),         // 1: response.PixKeyDeleteResponse
	(*PixKeyUpdateResponse)(nil),         // 2: response.PixKeyUpdateResponse
	(*PixKeyListByAccountResponse)(nil),  // 3: response.PixKeyListByAccountResponse
	(*PixKeyIsExistResponse)(nil),        // 4: response.PixKeyIsExistResponse
	(*PixKeyGetResponse)(nil),            // 5: response.PixKeyGetResponse
	(*NotifyAccountClosureResponse)(nil), // 6: response.NotifyAccountClosureResponse
	(*PixKeyClaimResponse)(nil),          // 7: response.PixKeyClaimResponse
	(wallet.PixKeyCreateResult)(0),       // 8: wallet.PixKeyCreateResult
	(*wallet.PixKey)(nil),                // 9: wallet.PixKey
	(*timestamppb.Timestamp)(nil),        // 10: google.protobuf.Timestamp
	(wallet.CommonResult)(0),             // 11: wallet.CommonResult
	(*wallet.BankAccount)(nil),           // 12: wallet.BankAccount
	(*wallet.BankAccountHolder)(nil),     // 13: wallet.BankAccountHolder
	(wallet.PixKeyClaimStatus)(0),        // 14: wallet.PixKeyClaimStatus
}
var file_services_response_pixDictServiceResponse_proto_depIdxs = []int32{
	8,  // 0: response.PixKeyCreateResponse.result:type_name -> wallet.PixKeyCreateResult
	9,  // 1: response.PixKeyCreateResponse.pix_key:type_name -> wallet.PixKey
	10, // 2: response.PixKeyCreateResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	10, // 3: response.PixKeyCreateResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	9,  // 4: response.PixKeyDeleteResponse.pix_key:type_name -> wallet.PixKey
	10, // 5: response.PixKeyDeleteResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	10, // 6: response.PixKeyDeleteResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	11, // 7: response.PixKeyUpdateResponse.result:type_name -> wallet.CommonResult
	9,  // 8: response.PixKeyUpdateResponse.pix_key:type_name -> wallet.PixKey
	10, // 9: response.PixKeyUpdateResponse.key_creation_datetime:type_name -> google.protobuf.Timestamp
	10, // 10: response.PixKeyUpdateResponse.key_possession_start_datetime:type_name -> google.protobuf.Timestamp
	11, // 11: response.PixKeyListByAccountResponse.result:type_name -> wallet.CommonResult
	9,  // 12: response.PixKeyListByAccountResponse.pix_key:type_name -> wallet.PixKey
	11, // 13: response.PixKeyIsExistResponse.result:type_name -> wallet.CommonResult
	9,  // 14: response.PixKeyIsExistResponse.existent_pix_key:type_name -> wallet.PixKey
	11, // 15: response.PixKeyGetResponse.result:type_name -> wallet.CommonResult
	9,  // 16: response.PixKeyGetResponse.pix_key:type_name -> wallet.PixKey
	12, // 17: response.PixKeyGetResponse.bank_account:type_name -> wallet.BankAccount
	13, // 18: response.PixKeyGetResponse.bank_account_holder:type_name -> wallet.BankAccountHolder
	9,  // 19: response.NotifyAccountClosureResponse.pix_key:type_name -> wallet.PixKey
	8,  // 20: response.PixKeyClaimResponse.result:type_name -> wallet.PixKeyCreateResult
	14, // 21: response.PixKeyClaimResponse.claim_status:type_name -> wallet.PixKeyClaimStatus
	10, // 22: response.PixKeyClaimResponse.claim_resolution_limit:type_name -> google.protobuf.Timestamp
	10, // 23: response.PixKeyClaimResponse.claim_conclusion_limit:type_name -> google.protobuf.Timestamp
	10, // 24: response.PixKeyClaimResponse.claim_last_modified_at:type_name -> google.protobuf.Timestamp
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_services_response_pixDictServiceResponse_proto_init() }
func file_services_response_pixDictServiceResponse_proto_init() {
	if File_services_response_pixDictServiceResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_response_pixDictServiceResponse_proto_rawDesc), len(file_services_response_pixDictServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_response_pixDictServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_response_pixDictServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_response_pixDictServiceResponse_proto_msgTypes,
	}.Build()
	File_services_response_pixDictServiceResponse_proto = out.File
	file_services_response_pixDictServiceResponse_proto_goTypes = nil
	file_services_response_pixDictServiceResponse_proto_depIdxs = nil
}
