// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pixQRCodeService.proto

package services

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixQRCodeService_QRCodeStaticCreate_FullMethodName = "/services.PixQRCodeService/QRCodeStaticCreate"
	PixQRCodeService_QRCodeStaticDecode_FullMethodName = "/services.PixQRCodeService/QRCodeStaticDecode"
)

// PixQRCodeServiceClient is the client API for PixQRCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixQRCodeServiceClient interface {
	QRCodeStaticCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error)
	QRCodeStaticDecode(ctx context.Context, in *request.QRCodeStaticDecodeRequest, opts ...grpc.CallOption) (*response.QRCodeStaticDecodeResponse, error)
}

type pixQRCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixQRCodeServiceClient(cc grpc.ClientConnInterface) PixQRCodeServiceClient {
	return &pixQRCodeServiceClient{cc}
}

func (c *pixQRCodeServiceClient) QRCodeStaticCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticCreateResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeStaticCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixQRCodeServiceClient) QRCodeStaticDecode(ctx context.Context, in *request.QRCodeStaticDecodeRequest, opts ...grpc.CallOption) (*response.QRCodeStaticDecodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticDecodeResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeStaticDecode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixQRCodeServiceServer is the server API for PixQRCodeService service.
// All implementations must embed UnimplementedPixQRCodeServiceServer
// for forward compatibility.
type PixQRCodeServiceServer interface {
	QRCodeStaticCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error)
	QRCodeStaticDecode(context.Context, *request.QRCodeStaticDecodeRequest) (*response.QRCodeStaticDecodeResponse, error)
	mustEmbedUnimplementedPixQRCodeServiceServer()
}

// UnimplementedPixQRCodeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixQRCodeServiceServer struct{}

func (UnimplementedPixQRCodeServiceServer) QRCodeStaticCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeStaticCreate not implemented")
}
func (UnimplementedPixQRCodeServiceServer) QRCodeStaticDecode(context.Context, *request.QRCodeStaticDecodeRequest) (*response.QRCodeStaticDecodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeStaticDecode not implemented")
}
func (UnimplementedPixQRCodeServiceServer) mustEmbedUnimplementedPixQRCodeServiceServer() {}
func (UnimplementedPixQRCodeServiceServer) testEmbeddedByValue()                          {}

// UnsafePixQRCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixQRCodeServiceServer will
// result in compilation errors.
type UnsafePixQRCodeServiceServer interface {
	mustEmbedUnimplementedPixQRCodeServiceServer()
}

func RegisterPixQRCodeServiceServer(s grpc.ServiceRegistrar, srv PixQRCodeServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixQRCodeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixQRCodeService_ServiceDesc, srv)
}

func _PixQRCodeService_QRCodeStaticCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeStaticCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeStaticCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeStaticCreate(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixQRCodeService_QRCodeStaticDecode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticDecodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeStaticDecode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeStaticDecode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeStaticDecode(ctx, req.(*request.QRCodeStaticDecodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixQRCodeService_ServiceDesc is the grpc.ServiceDesc for PixQRCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixQRCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixQRCodeService",
	HandlerType: (*PixQRCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QRCodeStaticCreate",
			Handler:    _PixQRCodeService_QRCodeStaticCreate_Handler,
		},
		{
			MethodName: "QRCodeStaticDecode",
			Handler:    _PixQRCodeService_QRCodeStaticDecode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pixQRCodeService.proto",
}
