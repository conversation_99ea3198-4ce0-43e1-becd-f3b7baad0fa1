// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pix/pixTransactionService.proto

package pix

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixPaymentService_PixOutConfirm_FullMethodName                 = "/services.PixPaymentService/PixOutConfirm"
	PixPaymentService_PixTransactionGetByEndToEndId_FullMethodName = "/services.PixPaymentService/PixTransactionGetByEndToEndId"
	PixPaymentService_PixRefundCreate_FullMethodName               = "/services.PixPaymentService/PixRefundCreate"
)

// PixPaymentServiceClient is the client API for PixPaymentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixPaymentServiceClient interface {
	PixOutConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
	PixTransactionGetByEndToEndId(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
	PixRefundCreate(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
}

type pixPaymentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixPaymentServiceClient(cc grpc.ClientConnInterface) PixPaymentServiceClient {
	return &pixPaymentServiceClient{cc}
}

func (c *pixPaymentServiceClient) PixOutConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixPaymentService_PixOutConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixPaymentServiceClient) PixTransactionGetByEndToEndId(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixPaymentService_PixTransactionGetByEndToEndId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixPaymentServiceClient) PixRefundCreate(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixPaymentService_PixRefundCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixPaymentServiceServer is the server API for PixPaymentService service.
// All implementations must embed UnimplementedPixPaymentServiceServer
// for forward compatibility.
type PixPaymentServiceServer interface {
	PixOutConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	PixTransactionGetByEndToEndId(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	PixRefundCreate(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	mustEmbedUnimplementedPixPaymentServiceServer()
}

// UnimplementedPixPaymentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixPaymentServiceServer struct{}

func (UnimplementedPixPaymentServiceServer) PixOutConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixOutConfirm not implemented")
}
func (UnimplementedPixPaymentServiceServer) PixTransactionGetByEndToEndId(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixTransactionGetByEndToEndId not implemented")
}
func (UnimplementedPixPaymentServiceServer) PixRefundCreate(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixRefundCreate not implemented")
}
func (UnimplementedPixPaymentServiceServer) mustEmbedUnimplementedPixPaymentServiceServer() {}
func (UnimplementedPixPaymentServiceServer) testEmbeddedByValue()                           {}

// UnsafePixPaymentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixPaymentServiceServer will
// result in compilation errors.
type UnsafePixPaymentServiceServer interface {
	mustEmbedUnimplementedPixPaymentServiceServer()
}

func RegisterPixPaymentServiceServer(s grpc.ServiceRegistrar, srv PixPaymentServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixPaymentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixPaymentService_ServiceDesc, srv)
}

func _PixPaymentService_PixOutConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixPaymentServiceServer).PixOutConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixPaymentService_PixOutConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixPaymentServiceServer).PixOutConfirm(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixPaymentService_PixTransactionGetByEndToEndId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixPaymentServiceServer).PixTransactionGetByEndToEndId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixPaymentService_PixTransactionGetByEndToEndId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixPaymentServiceServer).PixTransactionGetByEndToEndId(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixPaymentService_PixRefundCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixPaymentServiceServer).PixRefundCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixPaymentService_PixRefundCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixPaymentServiceServer).PixRefundCreate(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixPaymentService_ServiceDesc is the grpc.ServiceDesc for PixPaymentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixPaymentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixPaymentService",
	HandlerType: (*PixPaymentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixOutConfirm",
			Handler:    _PixPaymentService_PixOutConfirm_Handler,
		},
		{
			MethodName: "PixTransactionGetByEndToEndId",
			Handler:    _PixPaymentService_PixTransactionGetByEndToEndId_Handler,
		},
		{
			MethodName: "PixRefundCreate",
			Handler:    _PixPaymentService_PixRefundCreate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pix/pixTransactionService.proto",
}
