// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pix/pixClaimService.proto

package pix

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixDictService_PixKeyClaimCreate_FullMethodName        = "/services.PixDictService/PixKeyClaimCreate"
	PixDictService_PixKeyClaimCancel_FullMethodName        = "/services.PixDictService/PixKeyClaimCancel"
	PixDictService_PixKeyClaimConfirm_FullMethodName       = "/services.PixDictService/PixKeyClaimConfirm"
	PixDictService_PixKeyClaimListByAccount_FullMethodName = "/services.PixDictService/PixKeyClaimListByAccount"
)

// PixDictServiceClient is the client API for PixDictService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixDictServiceClient interface {
	PixKeyClaimCreate(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
	PixKeyClaimCancel(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
	PixKeyClaimConfirm(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
	PixKeyClaimListByAccount(ctx context.Context, in *request.PixKeyClaimListRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error)
}

type pixDictServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixDictServiceClient(cc grpc.ClientConnInterface) PixDictServiceClient {
	return &pixDictServiceClient{cc}
}

func (c *pixDictServiceClient) PixKeyClaimCreate(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyClaimCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyClaimCancel(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyClaimCancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyClaimConfirm(ctx context.Context, in *request.PixKeyClaimRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyClaimConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixDictServiceClient) PixKeyClaimListByAccount(ctx context.Context, in *request.PixKeyClaimListRequest, opts ...grpc.CallOption) (*response.PixKeyClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyClaimResponse)
	err := c.cc.Invoke(ctx, PixDictService_PixKeyClaimListByAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixDictServiceServer is the server API for PixDictService service.
// All implementations must embed UnimplementedPixDictServiceServer
// for forward compatibility.
type PixDictServiceServer interface {
	PixKeyClaimCreate(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error)
	PixKeyClaimCancel(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error)
	PixKeyClaimConfirm(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error)
	PixKeyClaimListByAccount(context.Context, *request.PixKeyClaimListRequest) (*response.PixKeyClaimResponse, error)
	mustEmbedUnimplementedPixDictServiceServer()
}

// UnimplementedPixDictServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixDictServiceServer struct{}

func (UnimplementedPixDictServiceServer) PixKeyClaimCreate(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaimCreate not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyClaimCancel(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaimCancel not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyClaimConfirm(context.Context, *request.PixKeyClaimRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaimConfirm not implemented")
}
func (UnimplementedPixDictServiceServer) PixKeyClaimListByAccount(context.Context, *request.PixKeyClaimListRequest) (*response.PixKeyClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixKeyClaimListByAccount not implemented")
}
func (UnimplementedPixDictServiceServer) mustEmbedUnimplementedPixDictServiceServer() {}
func (UnimplementedPixDictServiceServer) testEmbeddedByValue()                        {}

// UnsafePixDictServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixDictServiceServer will
// result in compilation errors.
type UnsafePixDictServiceServer interface {
	mustEmbedUnimplementedPixDictServiceServer()
}

func RegisterPixDictServiceServer(s grpc.ServiceRegistrar, srv PixDictServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixDictServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixDictService_ServiceDesc, srv)
}

func _PixDictService_PixKeyClaimCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyClaimCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyClaimCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyClaimCreate(ctx, req.(*request.PixKeyClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyClaimCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyClaimCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyClaimCancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyClaimCancel(ctx, req.(*request.PixKeyClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyClaimConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyClaimConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyClaimConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyClaimConfirm(ctx, req.(*request.PixKeyClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixDictService_PixKeyClaimListByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyClaimListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixDictServiceServer).PixKeyClaimListByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixDictService_PixKeyClaimListByAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixDictServiceServer).PixKeyClaimListByAccount(ctx, req.(*request.PixKeyClaimListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixDictService_ServiceDesc is the grpc.ServiceDesc for PixDictService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixDictService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixDictService",
	HandlerType: (*PixDictServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixKeyClaimCreate",
			Handler:    _PixDictService_PixKeyClaimCreate_Handler,
		},
		{
			MethodName: "PixKeyClaimCancel",
			Handler:    _PixDictService_PixKeyClaimCancel_Handler,
		},
		{
			MethodName: "PixKeyClaimConfirm",
			Handler:    _PixDictService_PixKeyClaimConfirm_Handler,
		},
		{
			MethodName: "PixKeyClaimListByAccount",
			Handler:    _PixDictService_PixKeyClaimListByAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pix/pixClaimService.proto",
}
