// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/pixWebhookService.proto

package pix

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pix_pixWebhookService_proto protoreflect.FileDescriptor

const file_services_pix_pixWebhookService_proto_rawDesc = "" +
	"\n" +
	"$services/pix/pixWebhookService.proto\x12\bservices\x1a0services/pix/request/pixDictServiceRequest.proto\x1a2services/pix/response/pixDictServiceResponse.proto2\x84\x02\n" +
	"\x11PixWebhookService\x12O\n" +
	"\x0fPixInValidation\x12\x1c.request.PixKeyCreateRequest\x1a\x1e.response.PixKeyCreateResponse\x12L\n" +
	"\fPixInConfirm\x12\x1c.request.PixKeyCreateRequest\x1a\x1e.response.PixKeyCreateResponse\x12P\n" +
	"\x10PixRefundConfirm\x12\x1c.request.PixKeyCreateRequest\x1a\x1e.response.PixKeyCreateResponseBEZCgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pixb\x06proto3"

var file_services_pix_pixWebhookService_proto_goTypes = []any{
	(*request.PixKeyCreateRequest)(nil),   // 0: request.PixKeyCreateRequest
	(*response.PixKeyCreateResponse)(nil), // 1: response.PixKeyCreateResponse
}
var file_services_pix_pixWebhookService_proto_depIdxs = []int32{
	0, // 0: services.PixWebhookService.PixInValidation:input_type -> request.PixKeyCreateRequest
	0, // 1: services.PixWebhookService.PixInConfirm:input_type -> request.PixKeyCreateRequest
	0, // 2: services.PixWebhookService.PixRefundConfirm:input_type -> request.PixKeyCreateRequest
	1, // 3: services.PixWebhookService.PixInValidation:output_type -> response.PixKeyCreateResponse
	1, // 4: services.PixWebhookService.PixInConfirm:output_type -> response.PixKeyCreateResponse
	1, // 5: services.PixWebhookService.PixRefundConfirm:output_type -> response.PixKeyCreateResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pix_pixWebhookService_proto_init() }
func file_services_pix_pixWebhookService_proto_init() {
	if File_services_pix_pixWebhookService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_pixWebhookService_proto_rawDesc), len(file_services_pix_pixWebhookService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pix_pixWebhookService_proto_goTypes,
		DependencyIndexes: file_services_pix_pixWebhookService_proto_depIdxs,
	}.Build()
	File_services_pix_pixWebhookService_proto = out.File
	file_services_pix_pixWebhookService_proto_goTypes = nil
	file_services_pix_pixWebhookService_proto_depIdxs = nil
}
