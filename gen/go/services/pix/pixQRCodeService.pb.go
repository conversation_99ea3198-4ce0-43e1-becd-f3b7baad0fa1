// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/pixQRCodeService.proto

package pix

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pix_pixQRCodeService_proto protoreflect.FileDescriptor

const file_services_pix_pixQRCodeService_proto_rawDesc = "" +
	"\n" +
	"#services/pix/pixQRCodeService.proto\x12\bservices\x1a2services/pix/request/pixQRCodeServiceRequest.proto\x1a4services/pix/response/pixQRCodeServiceResponse.proto2\xfa\x03\n" +
	"\x10PixQRCodeService\x12^\n" +
	"\x12QRCodeStaticCreate\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticCreateResponse\x12a\n" +
	"\x15QRCodeStaticGetByTxId\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticDecodeResponse\x12_\n" +
	"\x13QRCodeDynamicCreate\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticCreateResponse\x12b\n" +
	"\x16QRCodeDynamicGetByTxId\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticCreateResponse\x12^\n" +
	"\x12QRCodeGetByPayload\x12\".request.QRCodeStaticCreateRequest\x1a$.response.QRCodeStaticCreateResponseBEZCgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pixb\x06proto3"

var file_services_pix_pixQRCodeService_proto_goTypes = []any{
	(*request.QRCodeStaticCreateRequest)(nil),   // 0: request.QRCodeStaticCreateRequest
	(*response.QRCodeStaticCreateResponse)(nil), // 1: response.QRCodeStaticCreateResponse
	(*response.QRCodeStaticDecodeResponse)(nil), // 2: response.QRCodeStaticDecodeResponse
}
var file_services_pix_pixQRCodeService_proto_depIdxs = []int32{
	0, // 0: services.PixQRCodeService.QRCodeStaticCreate:input_type -> request.QRCodeStaticCreateRequest
	0, // 1: services.PixQRCodeService.QRCodeStaticGetByTxId:input_type -> request.QRCodeStaticCreateRequest
	0, // 2: services.PixQRCodeService.QRCodeDynamicCreate:input_type -> request.QRCodeStaticCreateRequest
	0, // 3: services.PixQRCodeService.QRCodeDynamicGetByTxId:input_type -> request.QRCodeStaticCreateRequest
	0, // 4: services.PixQRCodeService.QRCodeGetByPayload:input_type -> request.QRCodeStaticCreateRequest
	1, // 5: services.PixQRCodeService.QRCodeStaticCreate:output_type -> response.QRCodeStaticCreateResponse
	2, // 6: services.PixQRCodeService.QRCodeStaticGetByTxId:output_type -> response.QRCodeStaticDecodeResponse
	1, // 7: services.PixQRCodeService.QRCodeDynamicCreate:output_type -> response.QRCodeStaticCreateResponse
	1, // 8: services.PixQRCodeService.QRCodeDynamicGetByTxId:output_type -> response.QRCodeStaticCreateResponse
	1, // 9: services.PixQRCodeService.QRCodeGetByPayload:output_type -> response.QRCodeStaticCreateResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pix_pixQRCodeService_proto_init() }
func file_services_pix_pixQRCodeService_proto_init() {
	if File_services_pix_pixQRCodeService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_pixQRCodeService_proto_rawDesc), len(file_services_pix_pixQRCodeService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pix_pixQRCodeService_proto_goTypes,
		DependencyIndexes: file_services_pix_pixQRCodeService_proto_depIdxs,
	}.Build()
	File_services_pix_pixQRCodeService_proto = out.File
	file_services_pix_pixQRCodeService_proto_goTypes = nil
	file_services_pix_pixQRCodeService_proto_depIdxs = nil
}
