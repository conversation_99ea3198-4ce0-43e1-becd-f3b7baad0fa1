// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/pixClaimService.proto

package pix

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pix_pixClaimService_proto protoreflect.FileDescriptor

const file_services_pix_pixClaimService_proto_rawDesc = "" +
	"\n" +
	"\"services/pix/pixClaimService.proto\x12\bservices\x1a1services/pix/request/pixClaimServiceRequest.proto\x1a3services/pix/response/pixClaimServiceResponse.proto2\xe0\x02\n" +
	"\x0ePixDictService\x12O\n" +
	"\x11PixKeyClaimCreate\x12\x1b.request.PixKeyClaimRequest\x1a\x1d.response.PixKeyClaimResponse\x12O\n" +
	"\x11PixKeyClaimCancel\x12\x1b.request.PixKeyClaimRequest\x1a\x1d.response.PixKeyClaimResponse\x12P\n" +
	"\x12PixKeyClaimConfirm\x12\x1b.request.PixKeyClaimRequest\x1a\x1d.response.PixKeyClaimResponse\x12Z\n" +
	"\x18PixKeyClaimListByAccount\x12\x1f.request.PixKeyClaimListRequest\x1a\x1d.response.PixKeyClaimResponseBEZCgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pixb\x06proto3"

var file_services_pix_pixClaimService_proto_goTypes = []any{
	(*request.PixKeyClaimRequest)(nil),     // 0: request.PixKeyClaimRequest
	(*request.PixKeyClaimListRequest)(nil), // 1: request.PixKeyClaimListRequest
	(*response.PixKeyClaimResponse)(nil),   // 2: response.PixKeyClaimResponse
}
var file_services_pix_pixClaimService_proto_depIdxs = []int32{
	0, // 0: services.PixDictService.PixKeyClaimCreate:input_type -> request.PixKeyClaimRequest
	0, // 1: services.PixDictService.PixKeyClaimCancel:input_type -> request.PixKeyClaimRequest
	0, // 2: services.PixDictService.PixKeyClaimConfirm:input_type -> request.PixKeyClaimRequest
	1, // 3: services.PixDictService.PixKeyClaimListByAccount:input_type -> request.PixKeyClaimListRequest
	2, // 4: services.PixDictService.PixKeyClaimCreate:output_type -> response.PixKeyClaimResponse
	2, // 5: services.PixDictService.PixKeyClaimCancel:output_type -> response.PixKeyClaimResponse
	2, // 6: services.PixDictService.PixKeyClaimConfirm:output_type -> response.PixKeyClaimResponse
	2, // 7: services.PixDictService.PixKeyClaimListByAccount:output_type -> response.PixKeyClaimResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pix_pixClaimService_proto_init() }
func file_services_pix_pixClaimService_proto_init() {
	if File_services_pix_pixClaimService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_pixClaimService_proto_rawDesc), len(file_services_pix_pixClaimService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pix_pixClaimService_proto_goTypes,
		DependencyIndexes: file_services_pix_pixClaimService_proto_depIdxs,
	}.Build()
	File_services_pix_pixClaimService_proto = out.File
	file_services_pix_pixClaimService_proto_goTypes = nil
	file_services_pix_pixClaimService_proto_depIdxs = nil
}
