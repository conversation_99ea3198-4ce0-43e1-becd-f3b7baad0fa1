// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/request/pixClaimServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyClaimRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyClaimRequest) Reset() {
	*x = PixKeyClaimRequest{}
	mi := &file_services_pix_request_pixClaimServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimRequest) ProtoMessage() {}

func (x *PixKeyClaimRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixClaimServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimRequest.ProtoReflect.Descriptor instead.
func (*PixKeyClaimRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixClaimServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyClaimRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyClaimRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyClaimRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyClaimRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyClaimRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyClaimRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type PixKeyClaimListRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyClaimListRequest) Reset() {
	*x = PixKeyClaimListRequest{}
	mi := &file_services_pix_request_pixClaimServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimListRequest) ProtoMessage() {}

func (x *PixKeyClaimListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixClaimServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimListRequest.ProtoReflect.Descriptor instead.
func (*PixKeyClaimListRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixClaimServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyClaimListRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyClaimListRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyClaimListRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyClaimListRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

var File_services_pix_request_pixClaimServiceRequest_proto protoreflect.FileDescriptor

const file_services_pix_request_pixClaimServiceRequest_proto_rawDesc = "" +
	"\n" +
	"1services/pix/request/pixClaimServiceRequest.proto\x12\arequest\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\"\xa6\x02\n" +
	"\x12PixKeyClaimRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xaa\x02\n" +
	"\x16PixKeyClaimListRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reasonBMZKgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/requestb\x06proto3"

var (
	file_services_pix_request_pixClaimServiceRequest_proto_rawDescOnce sync.Once
	file_services_pix_request_pixClaimServiceRequest_proto_rawDescData []byte
)

func file_services_pix_request_pixClaimServiceRequest_proto_rawDescGZIP() []byte {
	file_services_pix_request_pixClaimServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_pix_request_pixClaimServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_request_pixClaimServiceRequest_proto_rawDesc), len(file_services_pix_request_pixClaimServiceRequest_proto_rawDesc)))
	})
	return file_services_pix_request_pixClaimServiceRequest_proto_rawDescData
}

var file_services_pix_request_pixClaimServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_pix_request_pixClaimServiceRequest_proto_goTypes = []any{
	(*PixKeyClaimRequest)(nil),       // 0: request.PixKeyClaimRequest
	(*PixKeyClaimListRequest)(nil),   // 1: request.PixKeyClaimListRequest
	(*wallet.PixKey)(nil),            // 2: wallet.PixKey
	(*wallet.BankAccount)(nil),       // 3: wallet.BankAccount
	(*wallet.BankAccountHolder)(nil), // 4: wallet.BankAccountHolder
	(wallet.PixKeyCreateReason)(0),   // 5: wallet.PixKeyCreateReason
}
var file_services_pix_request_pixClaimServiceRequest_proto_depIdxs = []int32{
	2, // 0: request.PixKeyClaimRequest.pix_key:type_name -> wallet.PixKey
	3, // 1: request.PixKeyClaimRequest.bank_account:type_name -> wallet.BankAccount
	4, // 2: request.PixKeyClaimRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	5, // 3: request.PixKeyClaimRequest.reason:type_name -> wallet.PixKeyCreateReason
	2, // 4: request.PixKeyClaimListRequest.pix_key:type_name -> wallet.PixKey
	3, // 5: request.PixKeyClaimListRequest.bank_account:type_name -> wallet.BankAccount
	4, // 6: request.PixKeyClaimListRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	5, // 7: request.PixKeyClaimListRequest.reason:type_name -> wallet.PixKeyCreateReason
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_services_pix_request_pixClaimServiceRequest_proto_init() }
func file_services_pix_request_pixClaimServiceRequest_proto_init() {
	if File_services_pix_request_pixClaimServiceRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_request_pixClaimServiceRequest_proto_rawDesc), len(file_services_pix_request_pixClaimServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_request_pixClaimServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_pix_request_pixClaimServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_pix_request_pixClaimServiceRequest_proto_msgTypes,
	}.Build()
	File_services_pix_request_pixClaimServiceRequest_proto = out.File
	file_services_pix_request_pixClaimServiceRequest_proto_goTypes = nil
	file_services_pix_request_pixClaimServiceRequest_proto_depIdxs = nil
}
