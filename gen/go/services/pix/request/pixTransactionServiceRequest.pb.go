// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/request/pixTransactionServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixOutConfirmRequest struct {
	state                  protoimpl.MessageState    `protogen:"open.v1"`
	Header                 *Header                   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId              string                    `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	RequestTime            *timestamppb.Timestamp    `protobuf:"bytes,3,opt,name=request_time,json=requestTime,proto3" json:"request_time,omitempty"`
	PayoutType             wallet.PixPayoutType      `protobuf:"varint,4,opt,name=payout_type,json=payoutType,proto3,enum=wallet.PixPayoutType" json:"payout_type,omitempty"`
	PayoutPurpose          wallet.PixPayoutPurpose   `protobuf:"varint,5,opt,name=payout_purpose,json=payoutPurpose,proto3,enum=wallet.PixPayoutPurpose" json:"payout_purpose,omitempty"`
	Initiator_CNPJ         int32                     `protobuf:"varint,6,opt,name=initiator_CNPJ,json=initiatorCNPJ,proto3" json:"initiator_CNPJ,omitempty"`
	PayerBankAccount       *wallet.BankAccount       `protobuf:"bytes,7,opt,name=payer_bank_account,json=payerBankAccount,proto3" json:"payer_bank_account,omitempty"`
	PayerBankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,8,opt,name=payer_bank_account_holder,json=payerBankAccountHolder,proto3" json:"payer_bank_account_holder,omitempty"`
	PayeeBankAccount       *wallet.BankAccount       `protobuf:"bytes,9,opt,name=payee_bank_account,json=payeeBankAccount,proto3" json:"payee_bank_account,omitempty"`
	PayeeBankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,10,opt,name=payee_bank_account_holder,json=payeeBankAccountHolder,proto3" json:"payee_bank_account_holder,omitempty"`
	PayeePixKey            *wallet.PixKey            `protobuf:"bytes,11,opt,name=payee_pix_key,json=payeePixKey,proto3" json:"payee_pix_key,omitempty"`
	Amount                 float64                   `protobuf:"fixed64,12,opt,name=amount,proto3" json:"amount,omitempty"`
	EndToEndId             string                    `protobuf:"bytes,13,opt,name=end_to_end_id,json=endToEndId,proto3" json:"end_to_end_id,omitempty"`
	Txid                   string                    `protobuf:"bytes,14,opt,name=txid,proto3" json:"txid,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PixOutConfirmRequest) Reset() {
	*x = PixOutConfirmRequest{}
	mi := &file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixOutConfirmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixOutConfirmRequest) ProtoMessage() {}

func (x *PixOutConfirmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixOutConfirmRequest.ProtoReflect.Descriptor instead.
func (*PixOutConfirmRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixTransactionServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *PixOutConfirmRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PixOutConfirmRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixOutConfirmRequest) GetRequestTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RequestTime
	}
	return nil
}

func (x *PixOutConfirmRequest) GetPayoutType() wallet.PixPayoutType {
	if x != nil {
		return x.PayoutType
	}
	return wallet.PixPayoutType(0)
}

func (x *PixOutConfirmRequest) GetPayoutPurpose() wallet.PixPayoutPurpose {
	if x != nil {
		return x.PayoutPurpose
	}
	return wallet.PixPayoutPurpose(0)
}

func (x *PixOutConfirmRequest) GetInitiator_CNPJ() int32 {
	if x != nil {
		return x.Initiator_CNPJ
	}
	return 0
}

func (x *PixOutConfirmRequest) GetPayerBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.PayerBankAccount
	}
	return nil
}

func (x *PixOutConfirmRequest) GetPayerBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.PayerBankAccountHolder
	}
	return nil
}

func (x *PixOutConfirmRequest) GetPayeeBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.PayeeBankAccount
	}
	return nil
}

func (x *PixOutConfirmRequest) GetPayeeBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.PayeeBankAccountHolder
	}
	return nil
}

func (x *PixOutConfirmRequest) GetPayeePixKey() *wallet.PixKey {
	if x != nil {
		return x.PayeePixKey
	}
	return nil
}

func (x *PixOutConfirmRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PixOutConfirmRequest) GetEndToEndId() string {
	if x != nil {
		return x.EndToEndId
	}
	return ""
}

func (x *PixOutConfirmRequest) GetTxid() string {
	if x != nil {
		return x.Txid
	}
	return ""
}

type PixTransactionGetByEndToEndIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *Header                `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId     string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixTransactionGetByEndToEndIdRequest) Reset() {
	*x = PixTransactionGetByEndToEndIdRequest{}
	mi := &file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixTransactionGetByEndToEndIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixTransactionGetByEndToEndIdRequest) ProtoMessage() {}

func (x *PixTransactionGetByEndToEndIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixTransactionGetByEndToEndIdRequest.ProtoReflect.Descriptor instead.
func (*PixTransactionGetByEndToEndIdRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixTransactionServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *PixTransactionGetByEndToEndIdRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PixTransactionGetByEndToEndIdRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

var File_services_pix_request_pixTransactionServiceRequest_proto protoreflect.FileDescriptor

const file_services_pix_request_pixTransactionServiceRequest_proto_rawDesc = "" +
	"\n" +
	"7services/pix/request/pixTransactionServiceRequest.proto\x12\arequest\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(services/pix/request/commonRequest.proto\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\x1a\x1bwallet/pixTransaction.proto\"\xf2\x05\n" +
	"\x14PixOutConfirmRequest\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.request.HeaderR\x06header\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12=\n" +
	"\frequest_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\vrequestTime\x126\n" +
	"\vpayout_type\x18\x04 \x01(\x0e2\x15.wallet.PixPayoutTypeR\n" +
	"payoutType\x12?\n" +
	"\x0epayout_purpose\x18\x05 \x01(\x0e2\x18.wallet.PixPayoutPurposeR\rpayoutPurpose\x12%\n" +
	"\x0einitiator_CNPJ\x18\x06 \x01(\x05R\rinitiatorCNPJ\x12A\n" +
	"\x12payer_bank_account\x18\a \x01(\v2\x13.wallet.BankAccountR\x10payerBankAccount\x12T\n" +
	"\x19payer_bank_account_holder\x18\b \x01(\v2\x19.wallet.BankAccountHolderR\x16payerBankAccountHolder\x12A\n" +
	"\x12payee_bank_account\x18\t \x01(\v2\x13.wallet.BankAccountR\x10payeeBankAccount\x12T\n" +
	"\x19payee_bank_account_holder\x18\n" +
	" \x01(\v2\x19.wallet.BankAccountHolderR\x16payeeBankAccountHolder\x122\n" +
	"\rpayee_pix_key\x18\v \x01(\v2\x0e.wallet.PixKeyR\vpayeePixKey\x12\x16\n" +
	"\x06amount\x18\f \x01(\x01R\x06amount\x12!\n" +
	"\rend_to_end_id\x18\r \x01(\tR\n" +
	"endToEndId\x12\x12\n" +
	"\x04txid\x18\x0e \x01(\tR\x04txid\"n\n" +
	"$PixTransactionGetByEndToEndIdRequest\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.request.HeaderR\x06header\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestIdBMZKgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/requestb\x06proto3"

var (
	file_services_pix_request_pixTransactionServiceRequest_proto_rawDescOnce sync.Once
	file_services_pix_request_pixTransactionServiceRequest_proto_rawDescData []byte
)

func file_services_pix_request_pixTransactionServiceRequest_proto_rawDescGZIP() []byte {
	file_services_pix_request_pixTransactionServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_pix_request_pixTransactionServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_request_pixTransactionServiceRequest_proto_rawDesc), len(file_services_pix_request_pixTransactionServiceRequest_proto_rawDesc)))
	})
	return file_services_pix_request_pixTransactionServiceRequest_proto_rawDescData
}

var file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_services_pix_request_pixTransactionServiceRequest_proto_goTypes = []any{
	(*PixOutConfirmRequest)(nil),                 // 0: request.PixOutConfirmRequest
	(*PixTransactionGetByEndToEndIdRequest)(nil), // 1: request.PixTransactionGetByEndToEndIdRequest
	(*Header)(nil),                   // 2: request.Header
	(*timestamppb.Timestamp)(nil),    // 3: google.protobuf.Timestamp
	(wallet.PixPayoutType)(0),        // 4: wallet.PixPayoutType
	(wallet.PixPayoutPurpose)(0),     // 5: wallet.PixPayoutPurpose
	(*wallet.BankAccount)(nil),       // 6: wallet.BankAccount
	(*wallet.BankAccountHolder)(nil), // 7: wallet.BankAccountHolder
	(*wallet.PixKey)(nil),            // 8: wallet.PixKey
}
var file_services_pix_request_pixTransactionServiceRequest_proto_depIdxs = []int32{
	2,  // 0: request.PixOutConfirmRequest.header:type_name -> request.Header
	3,  // 1: request.PixOutConfirmRequest.request_time:type_name -> google.protobuf.Timestamp
	4,  // 2: request.PixOutConfirmRequest.payout_type:type_name -> wallet.PixPayoutType
	5,  // 3: request.PixOutConfirmRequest.payout_purpose:type_name -> wallet.PixPayoutPurpose
	6,  // 4: request.PixOutConfirmRequest.payer_bank_account:type_name -> wallet.BankAccount
	7,  // 5: request.PixOutConfirmRequest.payer_bank_account_holder:type_name -> wallet.BankAccountHolder
	6,  // 6: request.PixOutConfirmRequest.payee_bank_account:type_name -> wallet.BankAccount
	7,  // 7: request.PixOutConfirmRequest.payee_bank_account_holder:type_name -> wallet.BankAccountHolder
	8,  // 8: request.PixOutConfirmRequest.payee_pix_key:type_name -> wallet.PixKey
	2,  // 9: request.PixTransactionGetByEndToEndIdRequest.header:type_name -> request.Header
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_services_pix_request_pixTransactionServiceRequest_proto_init() }
func file_services_pix_request_pixTransactionServiceRequest_proto_init() {
	if File_services_pix_request_pixTransactionServiceRequest_proto != nil {
		return
	}
	file_services_pix_request_commonRequest_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_request_pixTransactionServiceRequest_proto_rawDesc), len(file_services_pix_request_pixTransactionServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_request_pixTransactionServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_pix_request_pixTransactionServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_pix_request_pixTransactionServiceRequest_proto_msgTypes,
	}.Build()
	File_services_pix_request_pixTransactionServiceRequest_proto = out.File
	file_services_pix_request_pixTransactionServiceRequest_proto_goTypes = nil
	file_services_pix_request_pixTransactionServiceRequest_proto_depIdxs = nil
}
