// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/request/pixParticipantServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *Header                `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyRequest) Reset() {
	*x = PixKeyPolicyRequest{}
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyRequest) ProtoMessage() {}

func (x *PixKeyPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyRequest.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixParticipantServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyPolicyRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type PixKeyPolicyStatusRequest struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Header        *Header                   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Policy        wallet.PixKeyBucketPolicy `protobuf:"varint,2,opt,name=policy,proto3,enum=wallet.PixKeyBucketPolicy" json:"policy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyStatusRequest) Reset() {
	*x = PixKeyPolicyStatusRequest{}
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyStatusRequest) ProtoMessage() {}

func (x *PixKeyPolicyStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyStatusRequest.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyStatusRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixParticipantServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyPolicyStatusRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PixKeyPolicyStatusRequest) GetPolicy() wallet.PixKeyBucketPolicy {
	if x != nil {
		return x.Policy
	}
	return wallet.PixKeyBucketPolicy(0)
}

type PixParticipantBalanceGetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *Header                `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixParticipantBalanceGetRequest) Reset() {
	*x = PixParticipantBalanceGetRequest{}
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixParticipantBalanceGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixParticipantBalanceGetRequest) ProtoMessage() {}

func (x *PixParticipantBalanceGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixParticipantBalanceGetRequest.ProtoReflect.Descriptor instead.
func (*PixParticipantBalanceGetRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixParticipantServiceRequest_proto_rawDescGZIP(), []int{2}
}

func (x *PixParticipantBalanceGetRequest) GetHeader() *Header {
	if x != nil {
		return x.Header
	}
	return nil
}

var File_services_pix_request_pixParticipantServiceRequest_proto protoreflect.FileDescriptor

const file_services_pix_request_pixParticipantServiceRequest_proto_rawDesc = "" +
	"\n" +
	"7services/pix/request/pixParticipantServiceRequest.proto\x12\arequest\x1a\x13wallet/pixKey.proto\x1a(services/pix/request/commonRequest.proto\">\n" +
	"\x13PixKeyPolicyRequest\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.request.HeaderR\x06header\"x\n" +
	"\x19PixKeyPolicyStatusRequest\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.request.HeaderR\x06header\x122\n" +
	"\x06policy\x18\x02 \x01(\x0e2\x1a.wallet.PixKeyBucketPolicyR\x06policy\"J\n" +
	"\x1fPixParticipantBalanceGetRequest\x12'\n" +
	"\x06header\x18\x01 \x01(\v2\x0f.request.HeaderR\x06headerBMZKgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/requestb\x06proto3"

var (
	file_services_pix_request_pixParticipantServiceRequest_proto_rawDescOnce sync.Once
	file_services_pix_request_pixParticipantServiceRequest_proto_rawDescData []byte
)

func file_services_pix_request_pixParticipantServiceRequest_proto_rawDescGZIP() []byte {
	file_services_pix_request_pixParticipantServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_pix_request_pixParticipantServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_request_pixParticipantServiceRequest_proto_rawDesc), len(file_services_pix_request_pixParticipantServiceRequest_proto_rawDesc)))
	})
	return file_services_pix_request_pixParticipantServiceRequest_proto_rawDescData
}

var file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_services_pix_request_pixParticipantServiceRequest_proto_goTypes = []any{
	(*PixKeyPolicyRequest)(nil),             // 0: request.PixKeyPolicyRequest
	(*PixKeyPolicyStatusRequest)(nil),       // 1: request.PixKeyPolicyStatusRequest
	(*PixParticipantBalanceGetRequest)(nil), // 2: request.PixParticipantBalanceGetRequest
	(*Header)(nil),                          // 3: request.Header
	(wallet.PixKeyBucketPolicy)(0),          // 4: wallet.PixKeyBucketPolicy
}
var file_services_pix_request_pixParticipantServiceRequest_proto_depIdxs = []int32{
	3, // 0: request.PixKeyPolicyRequest.header:type_name -> request.Header
	3, // 1: request.PixKeyPolicyStatusRequest.header:type_name -> request.Header
	4, // 2: request.PixKeyPolicyStatusRequest.policy:type_name -> wallet.PixKeyBucketPolicy
	3, // 3: request.PixParticipantBalanceGetRequest.header:type_name -> request.Header
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_services_pix_request_pixParticipantServiceRequest_proto_init() }
func file_services_pix_request_pixParticipantServiceRequest_proto_init() {
	if File_services_pix_request_pixParticipantServiceRequest_proto != nil {
		return
	}
	file_services_pix_request_commonRequest_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_request_pixParticipantServiceRequest_proto_rawDesc), len(file_services_pix_request_pixParticipantServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_request_pixParticipantServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_pix_request_pixParticipantServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_pix_request_pixParticipantServiceRequest_proto_msgTypes,
	}.Build()
	File_services_pix_request_pixParticipantServiceRequest_proto = out.File
	file_services_pix_request_pixParticipantServiceRequest_proto_goTypes = nil
	file_services_pix_request_pixParticipantServiceRequest_proto_depIdxs = nil
}
