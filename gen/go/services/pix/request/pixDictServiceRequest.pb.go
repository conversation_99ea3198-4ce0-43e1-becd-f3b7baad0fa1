// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/request/pixDictServiceRequest.proto

package request

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyCreateRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,4,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,5,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,6,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyCreateRequest) Reset() {
	*x = PixKeyCreateRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyCreateRequest) ProtoMessage() {}

func (x *PixKeyCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyCreateRequest.ProtoReflect.Descriptor instead.
func (*PixKeyCreateRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyCreateRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyCreateRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyCreateRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyCreateRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyCreateRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyCreateRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type PixKeyDeleteRequest struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	RequestId     string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb          int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey        *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	Reason        wallet.PixKeyDeleteReason `protobuf:"varint,4,opt,name=reason,proto3,enum=wallet.PixKeyDeleteReason" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyDeleteRequest) Reset() {
	*x = PixKeyDeleteRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyDeleteRequest) ProtoMessage() {}

func (x *PixKeyDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyDeleteRequest.ProtoReflect.Descriptor instead.
func (*PixKeyDeleteRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyDeleteRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyDeleteRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyDeleteRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyDeleteRequest) GetReason() wallet.PixKeyDeleteReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyDeleteReason(0)
}

type PixKeyUpdateRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	PixKey            *wallet.PixKey            `protobuf:"bytes,3,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,5,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,6,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	Reason            wallet.PixKeyCreateReason `protobuf:"varint,7,opt,name=reason,proto3,enum=wallet.PixKeyCreateReason" json:"reason,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyUpdateRequest) Reset() {
	*x = PixKeyUpdateRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyUpdateRequest) ProtoMessage() {}

func (x *PixKeyUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyUpdateRequest.ProtoReflect.Descriptor instead.
func (*PixKeyUpdateRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{2}
}

func (x *PixKeyUpdateRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyUpdateRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyUpdateRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

func (x *PixKeyUpdateRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyUpdateRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

func (x *PixKeyUpdateRequest) GetReason() wallet.PixKeyCreateReason {
	if x != nil {
		return x.Reason
	}
	return wallet.PixKeyCreateReason(0)
}

type PixKeyListByAccountRequest struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	RequestId         string                    `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb              int32                     `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	BankAccount       *wallet.BankAccount       `protobuf:"bytes,3,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	BankAccountHolder *wallet.BankAccountHolder `protobuf:"bytes,4,opt,name=bank_account_holder,json=bankAccountHolder,proto3" json:"bank_account_holder,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PixKeyListByAccountRequest) Reset() {
	*x = PixKeyListByAccountRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyListByAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyListByAccountRequest) ProtoMessage() {}

func (x *PixKeyListByAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyListByAccountRequest.ProtoReflect.Descriptor instead.
func (*PixKeyListByAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{3}
}

func (x *PixKeyListByAccountRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyListByAccountRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyListByAccountRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *PixKeyListByAccountRequest) GetBankAccountHolder() *wallet.BankAccountHolder {
	if x != nil {
		return x.BankAccountHolder
	}
	return nil
}

type PixKeyIsExistRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb          int32                  `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	Keys          []*wallet.PixKey       `protobuf:"bytes,3,rep,name=keys,proto3" json:"keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyIsExistRequest) Reset() {
	*x = PixKeyIsExistRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyIsExistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyIsExistRequest) ProtoMessage() {}

func (x *PixKeyIsExistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyIsExistRequest.ProtoReflect.Descriptor instead.
func (*PixKeyIsExistRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{4}
}

func (x *PixKeyIsExistRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyIsExistRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyIsExistRequest) GetKeys() []*wallet.PixKey {
	if x != nil {
		return x.Keys
	}
	return nil
}

type PixKeyGetRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	RequestId           string                 `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb                int32                  `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	E2EId               string                 `protobuf:"bytes,3,opt,name=e2e_id,json=e2eId,proto3" json:"e2e_id,omitempty"`
	RequesterDocumentId string                 `protobuf:"bytes,4,opt,name=requester_document_id,json=requesterDocumentId,proto3" json:"requester_document_id,omitempty"`
	PixKey              *wallet.PixKey         `protobuf:"bytes,5,opt,name=pix_key,json=pixKey,proto3" json:"pix_key,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PixKeyGetRequest) Reset() {
	*x = PixKeyGetRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyGetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyGetRequest) ProtoMessage() {}

func (x *PixKeyGetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyGetRequest.ProtoReflect.Descriptor instead.
func (*PixKeyGetRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{5}
}

func (x *PixKeyGetRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *PixKeyGetRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *PixKeyGetRequest) GetE2EId() string {
	if x != nil {
		return x.E2EId
	}
	return ""
}

func (x *PixKeyGetRequest) GetRequesterDocumentId() string {
	if x != nil {
		return x.RequesterDocumentId
	}
	return ""
}

func (x *PixKeyGetRequest) GetPixKey() *wallet.PixKey {
	if x != nil {
		return x.PixKey
	}
	return nil
}

type NotifyAccountClosureRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Ispb          int32                  `protobuf:"varint,2,opt,name=ispb,proto3" json:"ispb,omitempty"`
	BankAccount   *wallet.BankAccount    `protobuf:"bytes,3,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotifyAccountClosureRequest) Reset() {
	*x = NotifyAccountClosureRequest{}
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotifyAccountClosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyAccountClosureRequest) ProtoMessage() {}

func (x *NotifyAccountClosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_pixDictServiceRequest_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyAccountClosureRequest.ProtoReflect.Descriptor instead.
func (*NotifyAccountClosureRequest) Descriptor() ([]byte, []int) {
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP(), []int{6}
}

func (x *NotifyAccountClosureRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *NotifyAccountClosureRequest) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *NotifyAccountClosureRequest) GetBankAccount() *wallet.BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

var File_services_pix_request_pixDictServiceRequest_proto protoreflect.FileDescriptor

const file_services_pix_request_pixDictServiceRequest_proto_rawDesc = "" +
	"\n" +
	"0services/pix/request/pixDictServiceRequest.proto\x12\arequest\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\"\xa7\x02\n" +
	"\x13PixKeyCreateRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x04 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x05 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\x06 \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xa4\x01\n" +
	"\x13PixKeyDeleteRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x122\n" +
	"\x06reason\x18\x04 \x01(\x0e2\x1a.wallet.PixKeyDeleteReasonR\x06reason\"\xa7\x02\n" +
	"\x13PixKeyUpdateRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12'\n" +
	"\apix_key\x18\x03 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\x126\n" +
	"\fbank_account\x18\x05 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x06 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\x122\n" +
	"\x06reason\x18\a \x01(\x0e2\x1a.wallet.PixKeyCreateReasonR\x06reason\"\xd1\x01\n" +
	"\x1aPixKeyListByAccountRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x126\n" +
	"\fbank_account\x18\x03 \x01(\v2\x13.wallet.BankAccountR\vbankAccount\x12I\n" +
	"\x13bank_account_holder\x18\x04 \x01(\v2\x19.wallet.BankAccountHolderR\x11bankAccountHolder\"l\n" +
	"\x14PixKeyIsExistRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12\"\n" +
	"\x04keys\x18\x03 \x03(\v2\x0e.wallet.PixKeyR\x04keys\"\xb8\x01\n" +
	"\x10PixKeyGetRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x12\x15\n" +
	"\x06e2e_id\x18\x03 \x01(\tR\x05e2eId\x122\n" +
	"\x15requester_document_id\x18\x04 \x01(\tR\x13requesterDocumentId\x12'\n" +
	"\apix_key\x18\x05 \x01(\v2\x0e.wallet.PixKeyR\x06pixKey\"\x87\x01\n" +
	"\x1bNotifyAccountClosureRequest\x12\x1c\n" +
	"\trequestId\x18\x01 \x01(\tR\trequestId\x12\x12\n" +
	"\x04ispb\x18\x02 \x01(\x05R\x04ispb\x126\n" +
	"\fbank_account\x18\x03 \x01(\v2\x13.wallet.BankAccountR\vbankAccountBMZKgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/requestb\x06proto3"

var (
	file_services_pix_request_pixDictServiceRequest_proto_rawDescOnce sync.Once
	file_services_pix_request_pixDictServiceRequest_proto_rawDescData []byte
)

func file_services_pix_request_pixDictServiceRequest_proto_rawDescGZIP() []byte {
	file_services_pix_request_pixDictServiceRequest_proto_rawDescOnce.Do(func() {
		file_services_pix_request_pixDictServiceRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_request_pixDictServiceRequest_proto_rawDesc), len(file_services_pix_request_pixDictServiceRequest_proto_rawDesc)))
	})
	return file_services_pix_request_pixDictServiceRequest_proto_rawDescData
}

var file_services_pix_request_pixDictServiceRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_services_pix_request_pixDictServiceRequest_proto_goTypes = []any{
	(*PixKeyCreateRequest)(nil),         // 0: request.PixKeyCreateRequest
	(*PixKeyDeleteRequest)(nil),         // 1: request.PixKeyDeleteRequest
	(*PixKeyUpdateRequest)(nil),         // 2: request.PixKeyUpdateRequest
	(*PixKeyListByAccountRequest)(nil),  // 3: request.PixKeyListByAccountRequest
	(*PixKeyIsExistRequest)(nil),        // 4: request.PixKeyIsExistRequest
	(*PixKeyGetRequest)(nil),            // 5: request.PixKeyGetRequest
	(*NotifyAccountClosureRequest)(nil), // 6: request.NotifyAccountClosureRequest
	(*wallet.PixKey)(nil),               // 7: wallet.PixKey
	(*wallet.BankAccount)(nil),          // 8: wallet.BankAccount
	(*wallet.BankAccountHolder)(nil),    // 9: wallet.BankAccountHolder
	(wallet.PixKeyCreateReason)(0),      // 10: wallet.PixKeyCreateReason
	(wallet.PixKeyDeleteReason)(0),      // 11: wallet.PixKeyDeleteReason
}
var file_services_pix_request_pixDictServiceRequest_proto_depIdxs = []int32{
	7,  // 0: request.PixKeyCreateRequest.pix_key:type_name -> wallet.PixKey
	8,  // 1: request.PixKeyCreateRequest.bank_account:type_name -> wallet.BankAccount
	9,  // 2: request.PixKeyCreateRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	10, // 3: request.PixKeyCreateRequest.reason:type_name -> wallet.PixKeyCreateReason
	7,  // 4: request.PixKeyDeleteRequest.pix_key:type_name -> wallet.PixKey
	11, // 5: request.PixKeyDeleteRequest.reason:type_name -> wallet.PixKeyDeleteReason
	7,  // 6: request.PixKeyUpdateRequest.pix_key:type_name -> wallet.PixKey
	8,  // 7: request.PixKeyUpdateRequest.bank_account:type_name -> wallet.BankAccount
	9,  // 8: request.PixKeyUpdateRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	10, // 9: request.PixKeyUpdateRequest.reason:type_name -> wallet.PixKeyCreateReason
	8,  // 10: request.PixKeyListByAccountRequest.bank_account:type_name -> wallet.BankAccount
	9,  // 11: request.PixKeyListByAccountRequest.bank_account_holder:type_name -> wallet.BankAccountHolder
	7,  // 12: request.PixKeyIsExistRequest.keys:type_name -> wallet.PixKey
	7,  // 13: request.PixKeyGetRequest.pix_key:type_name -> wallet.PixKey
	8,  // 14: request.NotifyAccountClosureRequest.bank_account:type_name -> wallet.BankAccount
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_services_pix_request_pixDictServiceRequest_proto_init() }
func file_services_pix_request_pixDictServiceRequest_proto_init() {
	if File_services_pix_request_pixDictServiceRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_request_pixDictServiceRequest_proto_rawDesc), len(file_services_pix_request_pixDictServiceRequest_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_request_pixDictServiceRequest_proto_goTypes,
		DependencyIndexes: file_services_pix_request_pixDictServiceRequest_proto_depIdxs,
		MessageInfos:      file_services_pix_request_pixDictServiceRequest_proto_msgTypes,
	}.Build()
	File_services_pix_request_pixDictServiceRequest_proto = out.File
	file_services_pix_request_pixDictServiceRequest_proto_goTypes = nil
	file_services_pix_request_pixDictServiceRequest_proto_depIdxs = nil
}
