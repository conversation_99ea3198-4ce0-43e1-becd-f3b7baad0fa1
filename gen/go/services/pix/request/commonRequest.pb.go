// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/request/commonRequest.proto

package request

import (
	_ "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChannelAdapter int32

const (
	ChannelAdapter_Delbank ChannelAdapter = 0
	ChannelAdapter_A55_JD  ChannelAdapter = 1
)

// Enum value maps for ChannelAdapter.
var (
	ChannelAdapter_name = map[int32]string{
		0: "Delbank",
		1: "A55_JD",
	}
	ChannelAdapter_value = map[string]int32{
		"Delbank": 0,
		"A55_JD":  1,
	}
)

func (x ChannelAdapter) Enum() *ChannelAdapter {
	p := new(ChannelAdapter)
	*p = x
	return p
}

func (x ChannelAdapter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelAdapter) Descriptor() protoreflect.EnumDescriptor {
	return file_services_pix_request_commonRequest_proto_enumTypes[0].Descriptor()
}

func (ChannelAdapter) Type() protoreflect.EnumType {
	return &file_services_pix_request_commonRequest_proto_enumTypes[0]
}

func (x ChannelAdapter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelAdapter.Descriptor instead.
func (ChannelAdapter) EnumDescriptor() ([]byte, []int) {
	return file_services_pix_request_commonRequest_proto_rawDescGZIP(), []int{0}
}

type Header struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdempotenceId string                 `protobuf:"bytes,1,opt,name=idempotence_id,json=idempotenceId,proto3" json:"idempotence_id,omitempty"`
	Channel       ChannelAdapter         `protobuf:"varint,4,opt,name=channel,proto3,enum=request.ChannelAdapter" json:"channel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Header) Reset() {
	*x = Header{}
	mi := &file_services_pix_request_commonRequest_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_request_commonRequest_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_services_pix_request_commonRequest_proto_rawDescGZIP(), []int{0}
}

func (x *Header) GetIdempotenceId() string {
	if x != nil {
		return x.IdempotenceId
	}
	return ""
}

func (x *Header) GetChannel() ChannelAdapter {
	if x != nil {
		return x.Channel
	}
	return ChannelAdapter_Delbank
}

var File_services_pix_request_commonRequest_proto protoreflect.FileDescriptor

const file_services_pix_request_commonRequest_proto_rawDesc = "" +
	"\n" +
	"(services/pix/request/commonRequest.proto\x12\arequest\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\"b\n" +
	"\x06Header\x12%\n" +
	"\x0eidempotence_id\x18\x01 \x01(\tR\ridempotenceId\x121\n" +
	"\achannel\x18\x04 \x01(\x0e2\x17.request.ChannelAdapterR\achannel*)\n" +
	"\x0eChannelAdapter\x12\v\n" +
	"\aDelbank\x10\x00\x12\n" +
	"\n" +
	"\x06A55_JD\x10\x01BMZKgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/requestb\x06proto3"

var (
	file_services_pix_request_commonRequest_proto_rawDescOnce sync.Once
	file_services_pix_request_commonRequest_proto_rawDescData []byte
)

func file_services_pix_request_commonRequest_proto_rawDescGZIP() []byte {
	file_services_pix_request_commonRequest_proto_rawDescOnce.Do(func() {
		file_services_pix_request_commonRequest_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_request_commonRequest_proto_rawDesc), len(file_services_pix_request_commonRequest_proto_rawDesc)))
	})
	return file_services_pix_request_commonRequest_proto_rawDescData
}

var file_services_pix_request_commonRequest_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_services_pix_request_commonRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_services_pix_request_commonRequest_proto_goTypes = []any{
	(ChannelAdapter)(0), // 0: request.ChannelAdapter
	(*Header)(nil),      // 1: request.Header
}
var file_services_pix_request_commonRequest_proto_depIdxs = []int32{
	0, // 0: request.Header.channel:type_name -> request.ChannelAdapter
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_services_pix_request_commonRequest_proto_init() }
func file_services_pix_request_commonRequest_proto_init() {
	if File_services_pix_request_commonRequest_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_request_commonRequest_proto_rawDesc), len(file_services_pix_request_commonRequest_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_request_commonRequest_proto_goTypes,
		DependencyIndexes: file_services_pix_request_commonRequest_proto_depIdxs,
		EnumInfos:         file_services_pix_request_commonRequest_proto_enumTypes,
		MessageInfos:      file_services_pix_request_commonRequest_proto_msgTypes,
	}.Build()
	File_services_pix_request_commonRequest_proto = out.File
	file_services_pix_request_commonRequest_proto_goTypes = nil
	file_services_pix_request_commonRequest_proto_depIdxs = nil
}
