// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pix/pixWebhookService.proto

package pix

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixWebhookService_PixInValidation_FullMethodName  = "/services.PixWebhookService/PixInValidation"
	PixWebhookService_PixInConfirm_FullMethodName     = "/services.PixWebhookService/PixInConfirm"
	PixWebhookService_PixRefundConfirm_FullMethodName = "/services.PixWebhookService/PixRefundConfirm"
)

// PixWebhookServiceClient is the client API for PixWebhookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixWebhookServiceClient interface {
	PixInValidation(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
	PixInConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
	PixRefundConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error)
}

type pixWebhookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixWebhookServiceClient(cc grpc.ClientConnInterface) PixWebhookServiceClient {
	return &pixWebhookServiceClient{cc}
}

func (c *pixWebhookServiceClient) PixInValidation(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixWebhookService_PixInValidation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixWebhookServiceClient) PixInConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixWebhookService_PixInConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixWebhookServiceClient) PixRefundConfirm(ctx context.Context, in *request.PixKeyCreateRequest, opts ...grpc.CallOption) (*response.PixKeyCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyCreateResponse)
	err := c.cc.Invoke(ctx, PixWebhookService_PixRefundConfirm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixWebhookServiceServer is the server API for PixWebhookService service.
// All implementations must embed UnimplementedPixWebhookServiceServer
// for forward compatibility.
type PixWebhookServiceServer interface {
	PixInValidation(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	PixInConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	PixRefundConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error)
	mustEmbedUnimplementedPixWebhookServiceServer()
}

// UnimplementedPixWebhookServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixWebhookServiceServer struct{}

func (UnimplementedPixWebhookServiceServer) PixInValidation(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixInValidation not implemented")
}
func (UnimplementedPixWebhookServiceServer) PixInConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixInConfirm not implemented")
}
func (UnimplementedPixWebhookServiceServer) PixRefundConfirm(context.Context, *request.PixKeyCreateRequest) (*response.PixKeyCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixRefundConfirm not implemented")
}
func (UnimplementedPixWebhookServiceServer) mustEmbedUnimplementedPixWebhookServiceServer() {}
func (UnimplementedPixWebhookServiceServer) testEmbeddedByValue()                           {}

// UnsafePixWebhookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixWebhookServiceServer will
// result in compilation errors.
type UnsafePixWebhookServiceServer interface {
	mustEmbedUnimplementedPixWebhookServiceServer()
}

func RegisterPixWebhookServiceServer(s grpc.ServiceRegistrar, srv PixWebhookServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixWebhookServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixWebhookService_ServiceDesc, srv)
}

func _PixWebhookService_PixInValidation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixWebhookServiceServer).PixInValidation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixWebhookService_PixInValidation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixWebhookServiceServer).PixInValidation(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixWebhookService_PixInConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixWebhookServiceServer).PixInConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixWebhookService_PixInConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixWebhookServiceServer).PixInConfirm(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixWebhookService_PixRefundConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixWebhookServiceServer).PixRefundConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixWebhookService_PixRefundConfirm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixWebhookServiceServer).PixRefundConfirm(ctx, req.(*request.PixKeyCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixWebhookService_ServiceDesc is the grpc.ServiceDesc for PixWebhookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixWebhookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixWebhookService",
	HandlerType: (*PixWebhookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixInValidation",
			Handler:    _PixWebhookService_PixInValidation_Handler,
		},
		{
			MethodName: "PixInConfirm",
			Handler:    _PixWebhookService_PixInConfirm_Handler,
		},
		{
			MethodName: "PixRefundConfirm",
			Handler:    _PixWebhookService_PixRefundConfirm_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pix/pixWebhookService.proto",
}
