// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/response/commonResponse.proto

package response

import (
	_ "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorCode int32

const (
	ErrorCode_INTERNAL_ERROR         ErrorCode = 0
	ErrorCode_ERROR_CHANNEL_REQUEST  ErrorCode = 1
	ErrorCode_ERROR_CHANNEL_BUSINESS ErrorCode = 2
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0: "INTERNAL_ERROR",
		1: "ERROR_CHANNEL_REQUEST",
		2: "ERROR_CHANNEL_BUSINESS",
	}
	ErrorCode_value = map[string]int32{
		"INTERNAL_ERROR":         0,
		"ERROR_CHANNEL_REQUEST":  1,
		"ERROR_CHANNEL_BUSINESS": 2,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_services_pix_response_commonResponse_proto_enumTypes[0].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_services_pix_response_commonResponse_proto_enumTypes[0]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_services_pix_response_commonResponse_proto_rawDescGZIP(), []int{0}
}

type Error struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         ErrorCode              `protobuf:"varint,1,opt,name=error,proto3,enum=request.ErrorCode" json:"error,omitempty"`
	ErrorSubcode  string                 `protobuf:"bytes,2,opt,name=error_subcode,json=errorSubcode,proto3" json:"error_subcode,omitempty"`
	ErrorDetails  string                 `protobuf:"bytes,3,opt,name=error_details,json=errorDetails,proto3" json:"error_details,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_services_pix_response_commonResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_commonResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_services_pix_response_commonResponse_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetError() ErrorCode {
	if x != nil {
		return x.Error
	}
	return ErrorCode_INTERNAL_ERROR
}

func (x *Error) GetErrorSubcode() string {
	if x != nil {
		return x.ErrorSubcode
	}
	return ""
}

func (x *Error) GetErrorDetails() string {
	if x != nil {
		return x.ErrorDetails
	}
	return ""
}

func (x *Error) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_services_pix_response_commonResponse_proto protoreflect.FileDescriptor

const file_services_pix_response_commonResponse_proto_rawDesc = "" +
	"\n" +
	"*services/pix/response/commonResponse.proto\x12\arequest\x1a\x14wallet/banking.proto\x1a\x13wallet/pixKey.proto\"\xa0\x01\n" +
	"\x05Error\x12(\n" +
	"\x05error\x18\x01 \x01(\x0e2\x12.request.ErrorCodeR\x05error\x12#\n" +
	"\rerror_subcode\x18\x02 \x01(\tR\ferrorSubcode\x12#\n" +
	"\rerror_details\x18\x03 \x01(\tR\ferrorDetails\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage*V\n" +
	"\tErrorCode\x12\x12\n" +
	"\x0eINTERNAL_ERROR\x10\x00\x12\x19\n" +
	"\x15ERROR_CHANNEL_REQUEST\x10\x01\x12\x1a\n" +
	"\x16ERROR_CHANNEL_BUSINESS\x10\x02BNZLgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/responseb\x06proto3"

var (
	file_services_pix_response_commonResponse_proto_rawDescOnce sync.Once
	file_services_pix_response_commonResponse_proto_rawDescData []byte
)

func file_services_pix_response_commonResponse_proto_rawDescGZIP() []byte {
	file_services_pix_response_commonResponse_proto_rawDescOnce.Do(func() {
		file_services_pix_response_commonResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_response_commonResponse_proto_rawDesc), len(file_services_pix_response_commonResponse_proto_rawDesc)))
	})
	return file_services_pix_response_commonResponse_proto_rawDescData
}

var file_services_pix_response_commonResponse_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_services_pix_response_commonResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_services_pix_response_commonResponse_proto_goTypes = []any{
	(ErrorCode)(0), // 0: request.ErrorCode
	(*Error)(nil),  // 1: request.Error
}
var file_services_pix_response_commonResponse_proto_depIdxs = []int32{
	0, // 0: request.Error.error:type_name -> request.ErrorCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_services_pix_response_commonResponse_proto_init() }
func file_services_pix_response_commonResponse_proto_init() {
	if File_services_pix_response_commonResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_response_commonResponse_proto_rawDesc), len(file_services_pix_response_commonResponse_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_response_commonResponse_proto_goTypes,
		DependencyIndexes: file_services_pix_response_commonResponse_proto_depIdxs,
		EnumInfos:         file_services_pix_response_commonResponse_proto_enumTypes,
		MessageInfos:      file_services_pix_response_commonResponse_proto_msgTypes,
	}.Build()
	File_services_pix_response_commonResponse_proto = out.File
	file_services_pix_response_commonResponse_proto_goTypes = nil
	file_services_pix_response_commonResponse_proto_depIdxs = nil
}
