// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/response/pixTransactionServiceResponse.proto

package response

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
type PixOutConfirmResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*PixOutConfirmResponse_Response
	//	*PixOutConfirmResponse_Error
	Result        isPixOutConfirmResponse_Result `protobuf_oneof:"result"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixOutConfirmResponse) Reset() {
	*x = PixOutConfirmResponse{}
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixOutConfirmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixOutConfirmResponse) ProtoMessage() {}

func (x *PixOutConfirmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixOutConfirmResponse.ProtoReflect.Descriptor instead.
func (*PixOutConfirmResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixTransactionServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *PixOutConfirmResponse) GetResult() isPixOutConfirmResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PixOutConfirmResponse) GetResponse() *PixOutConfirmResponseSuccess {
	if x != nil {
		if x, ok := x.Result.(*PixOutConfirmResponse_Response); ok {
			return x.Response
		}
	}
	return nil
}

func (x *PixOutConfirmResponse) GetError() *Error {
	if x != nil {
		if x, ok := x.Result.(*PixOutConfirmResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isPixOutConfirmResponse_Result interface {
	isPixOutConfirmResponse_Result()
}

type PixOutConfirmResponse_Response struct {
	Response *PixOutConfirmResponseSuccess `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type PixOutConfirmResponse_Error struct {
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PixOutConfirmResponse_Response) isPixOutConfirmResponse_Result() {}

func (*PixOutConfirmResponse_Error) isPixOutConfirmResponse_Result() {}

type PixOutConfirmResponseSuccess struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixOutConfirmResponseSuccess) Reset() {
	*x = PixOutConfirmResponseSuccess{}
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixOutConfirmResponseSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixOutConfirmResponseSuccess) ProtoMessage() {}

func (x *PixOutConfirmResponseSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixOutConfirmResponseSuccess.ProtoReflect.Descriptor instead.
func (*PixOutConfirmResponseSuccess) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixTransactionServiceResponse_proto_rawDescGZIP(), []int{1}
}

// *
type PixTransactionGetByEndToEndIdResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*PixTransactionGetByEndToEndIdResponse_Response
	//	*PixTransactionGetByEndToEndIdResponse_Error
	Result        isPixTransactionGetByEndToEndIdResponse_Result `protobuf_oneof:"result"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixTransactionGetByEndToEndIdResponse) Reset() {
	*x = PixTransactionGetByEndToEndIdResponse{}
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixTransactionGetByEndToEndIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixTransactionGetByEndToEndIdResponse) ProtoMessage() {}

func (x *PixTransactionGetByEndToEndIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixTransactionGetByEndToEndIdResponse.ProtoReflect.Descriptor instead.
func (*PixTransactionGetByEndToEndIdResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixTransactionServiceResponse_proto_rawDescGZIP(), []int{2}
}

func (x *PixTransactionGetByEndToEndIdResponse) GetResult() isPixTransactionGetByEndToEndIdResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PixTransactionGetByEndToEndIdResponse) GetResponse() *PixTransactionGetByEndToEndIdResponseSuccess {
	if x != nil {
		if x, ok := x.Result.(*PixTransactionGetByEndToEndIdResponse_Response); ok {
			return x.Response
		}
	}
	return nil
}

func (x *PixTransactionGetByEndToEndIdResponse) GetError() *Error {
	if x != nil {
		if x, ok := x.Result.(*PixTransactionGetByEndToEndIdResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isPixTransactionGetByEndToEndIdResponse_Result interface {
	isPixTransactionGetByEndToEndIdResponse_Result()
}

type PixTransactionGetByEndToEndIdResponse_Response struct {
	Response *PixTransactionGetByEndToEndIdResponseSuccess `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type PixTransactionGetByEndToEndIdResponse_Error struct {
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PixTransactionGetByEndToEndIdResponse_Response) isPixTransactionGetByEndToEndIdResponse_Result() {
}

func (*PixTransactionGetByEndToEndIdResponse_Error) isPixTransactionGetByEndToEndIdResponse_Result() {
}

type PixTransactionGetByEndToEndIdResponseSuccess struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixTransactionGetByEndToEndIdResponseSuccess) Reset() {
	*x = PixTransactionGetByEndToEndIdResponseSuccess{}
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixTransactionGetByEndToEndIdResponseSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixTransactionGetByEndToEndIdResponseSuccess) ProtoMessage() {}

func (x *PixTransactionGetByEndToEndIdResponseSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixTransactionGetByEndToEndIdResponseSuccess.ProtoReflect.Descriptor instead.
func (*PixTransactionGetByEndToEndIdResponseSuccess) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixTransactionServiceResponse_proto_rawDescGZIP(), []int{3}
}

var File_services_pix_response_pixTransactionServiceResponse_proto protoreflect.FileDescriptor

const file_services_pix_response_pixTransactionServiceResponse_proto_rawDesc = "" +
	"\n" +
	"9services/pix/response/pixTransactionServiceResponse.proto\x12\bresponse\x1a*services/pix/response/commonResponse.proto\"\x8f\x01\n" +
	"\x15PixOutConfirmResponse\x12D\n" +
	"\bresponse\x18\x01 \x01(\v2&.response.PixOutConfirmResponseSuccessH\x00R\bresponse\x12&\n" +
	"\x05error\x18\x02 \x01(\v2\x0e.request.ErrorH\x00R\x05errorB\b\n" +
	"\x06result\"\x1e\n" +
	"\x1cPixOutConfirmResponseSuccess\"\xaf\x01\n" +
	"%PixTransactionGetByEndToEndIdResponse\x12T\n" +
	"\bresponse\x18\x01 \x01(\v26.response.PixTransactionGetByEndToEndIdResponseSuccessH\x00R\bresponse\x12&\n" +
	"\x05error\x18\x02 \x01(\v2\x0e.request.ErrorH\x00R\x05errorB\b\n" +
	"\x06result\".\n" +
	",PixTransactionGetByEndToEndIdResponseSuccessBNZLgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/responseb\x06proto3"

var (
	file_services_pix_response_pixTransactionServiceResponse_proto_rawDescOnce sync.Once
	file_services_pix_response_pixTransactionServiceResponse_proto_rawDescData []byte
)

func file_services_pix_response_pixTransactionServiceResponse_proto_rawDescGZIP() []byte {
	file_services_pix_response_pixTransactionServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_pix_response_pixTransactionServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_response_pixTransactionServiceResponse_proto_rawDesc), len(file_services_pix_response_pixTransactionServiceResponse_proto_rawDesc)))
	})
	return file_services_pix_response_pixTransactionServiceResponse_proto_rawDescData
}

var file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_services_pix_response_pixTransactionServiceResponse_proto_goTypes = []any{
	(*PixOutConfirmResponse)(nil),                        // 0: response.PixOutConfirmResponse
	(*PixOutConfirmResponseSuccess)(nil),                 // 1: response.PixOutConfirmResponseSuccess
	(*PixTransactionGetByEndToEndIdResponse)(nil),        // 2: response.PixTransactionGetByEndToEndIdResponse
	(*PixTransactionGetByEndToEndIdResponseSuccess)(nil), // 3: response.PixTransactionGetByEndToEndIdResponseSuccess
	(*Error)(nil), // 4: request.Error
}
var file_services_pix_response_pixTransactionServiceResponse_proto_depIdxs = []int32{
	1, // 0: response.PixOutConfirmResponse.response:type_name -> response.PixOutConfirmResponseSuccess
	4, // 1: response.PixOutConfirmResponse.error:type_name -> request.Error
	3, // 2: response.PixTransactionGetByEndToEndIdResponse.response:type_name -> response.PixTransactionGetByEndToEndIdResponseSuccess
	4, // 3: response.PixTransactionGetByEndToEndIdResponse.error:type_name -> request.Error
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_services_pix_response_pixTransactionServiceResponse_proto_init() }
func file_services_pix_response_pixTransactionServiceResponse_proto_init() {
	if File_services_pix_response_pixTransactionServiceResponse_proto != nil {
		return
	}
	file_services_pix_response_commonResponse_proto_init()
	file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[0].OneofWrappers = []any{
		(*PixOutConfirmResponse_Response)(nil),
		(*PixOutConfirmResponse_Error)(nil),
	}
	file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes[2].OneofWrappers = []any{
		(*PixTransactionGetByEndToEndIdResponse_Response)(nil),
		(*PixTransactionGetByEndToEndIdResponse_Error)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_response_pixTransactionServiceResponse_proto_rawDesc), len(file_services_pix_response_pixTransactionServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_response_pixTransactionServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_pix_response_pixTransactionServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_pix_response_pixTransactionServiceResponse_proto_msgTypes,
	}.Build()
	File_services_pix_response_pixTransactionServiceResponse_proto = out.File
	file_services_pix_response_pixTransactionServiceResponse_proto_goTypes = nil
	file_services_pix_response_pixTransactionServiceResponse_proto_depIdxs = nil
}
