// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.7
// 	protoc        v5.29.3
// source: services/pix/response/pixParticipantServiceResponse.proto

package response

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
type PixKeyPolicyResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*PixKeyPolicyResponse_Response
	//	*PixKeyPolicyResponse_Error
	Result        isPixKeyPolicyResponse_Result `protobuf_oneof:"result"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyResponse) Reset() {
	*x = PixKeyPolicyResponse{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyResponse) ProtoMessage() {}

func (x *PixKeyPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyResponse.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyPolicyResponse) GetResult() isPixKeyPolicyResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PixKeyPolicyResponse) GetResponse() *PixKeyPolicyResponseSuccess {
	if x != nil {
		if x, ok := x.Result.(*PixKeyPolicyResponse_Response); ok {
			return x.Response
		}
	}
	return nil
}

func (x *PixKeyPolicyResponse) GetError() *Error {
	if x != nil {
		if x, ok := x.Result.(*PixKeyPolicyResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isPixKeyPolicyResponse_Result interface {
	isPixKeyPolicyResponse_Result()
}

type PixKeyPolicyResponse_Response struct {
	Response *PixKeyPolicyResponseSuccess `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type PixKeyPolicyResponse_Error struct {
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PixKeyPolicyResponse_Response) isPixKeyPolicyResponse_Result() {}

func (*PixKeyPolicyResponse_Error) isPixKeyPolicyResponse_Result() {}

type PixKeyPolicyResponseSuccess struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BucketCategory string                 `protobuf:"bytes,2,opt,name=bucket_category,json=bucketCategory,proto3" json:"bucket_category,omitempty"`
	Policy         *wallet.PixKeyPolicy   `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixKeyPolicyResponseSuccess) Reset() {
	*x = PixKeyPolicyResponseSuccess{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyResponseSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyResponseSuccess) ProtoMessage() {}

func (x *PixKeyPolicyResponseSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyResponseSuccess.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyResponseSuccess) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyPolicyResponseSuccess) GetBucketCategory() string {
	if x != nil {
		return x.BucketCategory
	}
	return ""
}

func (x *PixKeyPolicyResponseSuccess) GetPolicy() *wallet.PixKeyPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

// *
type PixKeyPolicyStatusResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*PixKeyPolicyStatusResponse_Response
	//	*PixKeyPolicyStatusResponse_Error
	Result        isPixKeyPolicyStatusResponse_Result `protobuf_oneof:"result"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKeyPolicyStatusResponse) Reset() {
	*x = PixKeyPolicyStatusResponse{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyStatusResponse) ProtoMessage() {}

func (x *PixKeyPolicyStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyStatusResponse.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyStatusResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{2}
}

func (x *PixKeyPolicyStatusResponse) GetResult() isPixKeyPolicyStatusResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PixKeyPolicyStatusResponse) GetResponse() *PixKeyPolicyStatusResponseSuccess {
	if x != nil {
		if x, ok := x.Result.(*PixKeyPolicyStatusResponse_Response); ok {
			return x.Response
		}
	}
	return nil
}

func (x *PixKeyPolicyStatusResponse) GetError() *Error {
	if x != nil {
		if x, ok := x.Result.(*PixKeyPolicyStatusResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isPixKeyPolicyStatusResponse_Result interface {
	isPixKeyPolicyStatusResponse_Result()
}

type PixKeyPolicyStatusResponse_Response struct {
	Response *PixKeyPolicyStatusResponseSuccess `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type PixKeyPolicyStatusResponse_Error struct {
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PixKeyPolicyStatusResponse_Response) isPixKeyPolicyStatusResponse_Result() {}

func (*PixKeyPolicyStatusResponse_Error) isPixKeyPolicyStatusResponse_Result() {}

type PixKeyPolicyStatusResponseSuccess struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BucketCategory string                 `protobuf:"bytes,2,opt,name=bucket_category,json=bucketCategory,proto3" json:"bucket_category,omitempty"`
	Policy         *wallet.PixKeyPolicy   `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixKeyPolicyStatusResponseSuccess) Reset() {
	*x = PixKeyPolicyStatusResponseSuccess{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicyStatusResponseSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicyStatusResponseSuccess) ProtoMessage() {}

func (x *PixKeyPolicyStatusResponseSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicyStatusResponseSuccess.ProtoReflect.Descriptor instead.
func (*PixKeyPolicyStatusResponseSuccess) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{3}
}

func (x *PixKeyPolicyStatusResponseSuccess) GetBucketCategory() string {
	if x != nil {
		return x.BucketCategory
	}
	return ""
}

func (x *PixKeyPolicyStatusResponseSuccess) GetPolicy() *wallet.PixKeyPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

// *
type PixParticipantBalanceGetResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*PixParticipantBalanceGetResponse_Response
	//	*PixParticipantBalanceGetResponse_Error
	Result        isPixParticipantBalanceGetResponse_Result `protobuf_oneof:"result"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixParticipantBalanceGetResponse) Reset() {
	*x = PixParticipantBalanceGetResponse{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixParticipantBalanceGetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixParticipantBalanceGetResponse) ProtoMessage() {}

func (x *PixParticipantBalanceGetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixParticipantBalanceGetResponse.ProtoReflect.Descriptor instead.
func (*PixParticipantBalanceGetResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{4}
}

func (x *PixParticipantBalanceGetResponse) GetResult() isPixParticipantBalanceGetResponse_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PixParticipantBalanceGetResponse) GetResponse() *PixParticipantBalanceGetResponseSuccess {
	if x != nil {
		if x, ok := x.Result.(*PixParticipantBalanceGetResponse_Response); ok {
			return x.Response
		}
	}
	return nil
}

func (x *PixParticipantBalanceGetResponse) GetError() *Error {
	if x != nil {
		if x, ok := x.Result.(*PixParticipantBalanceGetResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isPixParticipantBalanceGetResponse_Result interface {
	isPixParticipantBalanceGetResponse_Result()
}

type PixParticipantBalanceGetResponse_Response struct {
	Response *PixParticipantBalanceGetResponseSuccess `protobuf:"bytes,1,opt,name=response,proto3,oneof"`
}

type PixParticipantBalanceGetResponse_Error struct {
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3,oneof"`
}

func (*PixParticipantBalanceGetResponse_Response) isPixParticipantBalanceGetResponse_Result() {}

func (*PixParticipantBalanceGetResponse_Error) isPixParticipantBalanceGetResponse_Result() {}

type PixParticipantBalanceGetResponseSuccess struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BucketCategory string                 `protobuf:"bytes,2,opt,name=bucket_category,json=bucketCategory,proto3" json:"bucket_category,omitempty"`
	Policy         *wallet.PixKeyPolicy   `protobuf:"bytes,3,opt,name=policy,proto3" json:"policy,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PixParticipantBalanceGetResponseSuccess) Reset() {
	*x = PixParticipantBalanceGetResponseSuccess{}
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixParticipantBalanceGetResponseSuccess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixParticipantBalanceGetResponseSuccess) ProtoMessage() {}

func (x *PixParticipantBalanceGetResponseSuccess) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixParticipantBalanceGetResponseSuccess.ProtoReflect.Descriptor instead.
func (*PixParticipantBalanceGetResponseSuccess) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP(), []int{5}
}

func (x *PixParticipantBalanceGetResponseSuccess) GetBucketCategory() string {
	if x != nil {
		return x.BucketCategory
	}
	return ""
}

func (x *PixParticipantBalanceGetResponseSuccess) GetPolicy() *wallet.PixKeyPolicy {
	if x != nil {
		return x.Policy
	}
	return nil
}

var File_services_pix_response_pixParticipantServiceResponse_proto protoreflect.FileDescriptor

const file_services_pix_response_pixParticipantServiceResponse_proto_rawDesc = "" +
	"\n" +
	"9services/pix/response/pixParticipantServiceResponse.proto\x12\bresponse\x1a\x13wallet/pixKey.proto\x1a*services/pix/response/commonResponse.proto\"\x8d\x01\n" +
	"\x14PixKeyPolicyResponse\x12C\n" +
	"\bresponse\x18\x01 \x01(\v2%.response.PixKeyPolicyResponseSuccessH\x00R\bresponse\x12&\n" +
	"\x05error\x18\x02 \x01(\v2\x0e.request.ErrorH\x00R\x05errorB\b\n" +
	"\x06result\"t\n" +
	"\x1bPixKeyPolicyResponseSuccess\x12'\n" +
	"\x0fbucket_category\x18\x02 \x01(\tR\x0ebucketCategory\x12,\n" +
	"\x06policy\x18\x03 \x01(\v2\x14.wallet.PixKeyPolicyR\x06policy\"\x99\x01\n" +
	"\x1aPixKeyPolicyStatusResponse\x12I\n" +
	"\bresponse\x18\x01 \x01(\v2+.response.PixKeyPolicyStatusResponseSuccessH\x00R\bresponse\x12&\n" +
	"\x05error\x18\x02 \x01(\v2\x0e.request.ErrorH\x00R\x05errorB\b\n" +
	"\x06result\"z\n" +
	"!PixKeyPolicyStatusResponseSuccess\x12'\n" +
	"\x0fbucket_category\x18\x02 \x01(\tR\x0ebucketCategory\x12,\n" +
	"\x06policy\x18\x03 \x01(\v2\x14.wallet.PixKeyPolicyR\x06policy\"\xa5\x01\n" +
	" PixParticipantBalanceGetResponse\x12O\n" +
	"\bresponse\x18\x01 \x01(\v21.response.PixParticipantBalanceGetResponseSuccessH\x00R\bresponse\x12&\n" +
	"\x05error\x18\x02 \x01(\v2\x0e.request.ErrorH\x00R\x05errorB\b\n" +
	"\x06result\"\x80\x01\n" +
	"'PixParticipantBalanceGetResponseSuccess\x12'\n" +
	"\x0fbucket_category\x18\x02 \x01(\tR\x0ebucketCategory\x12,\n" +
	"\x06policy\x18\x03 \x01(\v2\x14.wallet.PixKeyPolicyR\x06policyBNZLgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/responseb\x06proto3"

var (
	file_services_pix_response_pixParticipantServiceResponse_proto_rawDescOnce sync.Once
	file_services_pix_response_pixParticipantServiceResponse_proto_rawDescData []byte
)

func file_services_pix_response_pixParticipantServiceResponse_proto_rawDescGZIP() []byte {
	file_services_pix_response_pixParticipantServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_pix_response_pixParticipantServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_response_pixParticipantServiceResponse_proto_rawDesc), len(file_services_pix_response_pixParticipantServiceResponse_proto_rawDesc)))
	})
	return file_services_pix_response_pixParticipantServiceResponse_proto_rawDescData
}

var file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_services_pix_response_pixParticipantServiceResponse_proto_goTypes = []any{
	(*PixKeyPolicyResponse)(nil),                    // 0: response.PixKeyPolicyResponse
	(*PixKeyPolicyResponseSuccess)(nil),             // 1: response.PixKeyPolicyResponseSuccess
	(*PixKeyPolicyStatusResponse)(nil),              // 2: response.PixKeyPolicyStatusResponse
	(*PixKeyPolicyStatusResponseSuccess)(nil),       // 3: response.PixKeyPolicyStatusResponseSuccess
	(*PixParticipantBalanceGetResponse)(nil),        // 4: response.PixParticipantBalanceGetResponse
	(*PixParticipantBalanceGetResponseSuccess)(nil), // 5: response.PixParticipantBalanceGetResponseSuccess
	(*Error)(nil),               // 6: request.Error
	(*wallet.PixKeyPolicy)(nil), // 7: wallet.PixKeyPolicy
}
var file_services_pix_response_pixParticipantServiceResponse_proto_depIdxs = []int32{
	1, // 0: response.PixKeyPolicyResponse.response:type_name -> response.PixKeyPolicyResponseSuccess
	6, // 1: response.PixKeyPolicyResponse.error:type_name -> request.Error
	7, // 2: response.PixKeyPolicyResponseSuccess.policy:type_name -> wallet.PixKeyPolicy
	3, // 3: response.PixKeyPolicyStatusResponse.response:type_name -> response.PixKeyPolicyStatusResponseSuccess
	6, // 4: response.PixKeyPolicyStatusResponse.error:type_name -> request.Error
	7, // 5: response.PixKeyPolicyStatusResponseSuccess.policy:type_name -> wallet.PixKeyPolicy
	5, // 6: response.PixParticipantBalanceGetResponse.response:type_name -> response.PixParticipantBalanceGetResponseSuccess
	6, // 7: response.PixParticipantBalanceGetResponse.error:type_name -> request.Error
	7, // 8: response.PixParticipantBalanceGetResponseSuccess.policy:type_name -> wallet.PixKeyPolicy
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_services_pix_response_pixParticipantServiceResponse_proto_init() }
func file_services_pix_response_pixParticipantServiceResponse_proto_init() {
	if File_services_pix_response_pixParticipantServiceResponse_proto != nil {
		return
	}
	file_services_pix_response_commonResponse_proto_init()
	file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[0].OneofWrappers = []any{
		(*PixKeyPolicyResponse_Response)(nil),
		(*PixKeyPolicyResponse_Error)(nil),
	}
	file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[2].OneofWrappers = []any{
		(*PixKeyPolicyStatusResponse_Response)(nil),
		(*PixKeyPolicyStatusResponse_Error)(nil),
	}
	file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes[4].OneofWrappers = []any{
		(*PixParticipantBalanceGetResponse_Response)(nil),
		(*PixParticipantBalanceGetResponse_Error)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_response_pixParticipantServiceResponse_proto_rawDesc), len(file_services_pix_response_pixParticipantServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_response_pixParticipantServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_pix_response_pixParticipantServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_pix_response_pixParticipantServiceResponse_proto_msgTypes,
	}.Build()
	File_services_pix_response_pixParticipantServiceResponse_proto = out.File
	file_services_pix_response_pixParticipantServiceResponse_proto_goTypes = nil
	file_services_pix_response_pixParticipantServiceResponse_proto_depIdxs = nil
}
