// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/response/pixClaimServiceResponse.proto

package response

import (
	wallet "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PixKeyClaimResponse struct {
	state                protoimpl.MessageState    `protogen:"open.v1"`
	Result               wallet.PixKeyCreateResult `protobuf:"varint,1,opt,name=result,proto3,enum=wallet.PixKeyCreateResult" json:"result,omitempty"`
	IspbDonator          int32                     `protobuf:"varint,2,opt,name=ispb_donator,json=ispbDonator,proto3" json:"ispb_donator,omitempty"`
	ClaimId              string                    `protobuf:"bytes,3,opt,name=claim_id,json=claimId,proto3" json:"claim_id,omitempty"`
	ClaimStatus          wallet.PixKeyClaimStatus  `protobuf:"varint,4,opt,name=claim_status,json=claimStatus,proto3,enum=wallet.PixKeyClaimStatus" json:"claim_status,omitempty"`
	ClaimResolutionLimit *timestamppb.Timestamp    `protobuf:"bytes,5,opt,name=claim_resolution_limit,json=claimResolutionLimit,proto3" json:"claim_resolution_limit,omitempty"` // Claim Resolution Limit Date and Time for donator
	ClaimConclusionLimit *timestamppb.Timestamp    `protobuf:"bytes,6,opt,name=claim_conclusion_limit,json=claimConclusionLimit,proto3" json:"claim_conclusion_limit,omitempty"` // Claim Limit for requester to confirm the claim
	ClaimLastModifiedAt  *timestamppb.Timestamp    `protobuf:"bytes,7,opt,name=claim_last_modified_at,json=claimLastModifiedAt,proto3" json:"claim_last_modified_at,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PixKeyClaimResponse) Reset() {
	*x = PixKeyClaimResponse{}
	mi := &file_services_pix_response_pixClaimServiceResponse_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyClaimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyClaimResponse) ProtoMessage() {}

func (x *PixKeyClaimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_pix_response_pixClaimServiceResponse_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyClaimResponse.ProtoReflect.Descriptor instead.
func (*PixKeyClaimResponse) Descriptor() ([]byte, []int) {
	return file_services_pix_response_pixClaimServiceResponse_proto_rawDescGZIP(), []int{0}
}

func (x *PixKeyClaimResponse) GetResult() wallet.PixKeyCreateResult {
	if x != nil {
		return x.Result
	}
	return wallet.PixKeyCreateResult(0)
}

func (x *PixKeyClaimResponse) GetIspbDonator() int32 {
	if x != nil {
		return x.IspbDonator
	}
	return 0
}

func (x *PixKeyClaimResponse) GetClaimId() string {
	if x != nil {
		return x.ClaimId
	}
	return ""
}

func (x *PixKeyClaimResponse) GetClaimStatus() wallet.PixKeyClaimStatus {
	if x != nil {
		return x.ClaimStatus
	}
	return wallet.PixKeyClaimStatus(0)
}

func (x *PixKeyClaimResponse) GetClaimResolutionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimResolutionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimConclusionLimit() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimConclusionLimit
	}
	return nil
}

func (x *PixKeyClaimResponse) GetClaimLastModifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ClaimLastModifiedAt
	}
	return nil
}

var File_services_pix_response_pixClaimServiceResponse_proto protoreflect.FileDescriptor

const file_services_pix_response_pixClaimServiceResponse_proto_rawDesc = "" +
	"\n" +
	"3services/pix/response/pixClaimServiceResponse.proto\x12\bresponse\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x13wallet/pixKey.proto\"\xba\x03\n" +
	"\x13PixKeyClaimResponse\x122\n" +
	"\x06result\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyCreateResultR\x06result\x12!\n" +
	"\fispb_donator\x18\x02 \x01(\x05R\vispbDonator\x12\x19\n" +
	"\bclaim_id\x18\x03 \x01(\tR\aclaimId\x12<\n" +
	"\fclaim_status\x18\x04 \x01(\x0e2\x19.wallet.PixKeyClaimStatusR\vclaimStatus\x12P\n" +
	"\x16claim_resolution_limit\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimResolutionLimit\x12P\n" +
	"\x16claim_conclusion_limit\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x14claimConclusionLimit\x12O\n" +
	"\x16claim_last_modified_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x13claimLastModifiedAtBNZLgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/responseb\x06proto3"

var (
	file_services_pix_response_pixClaimServiceResponse_proto_rawDescOnce sync.Once
	file_services_pix_response_pixClaimServiceResponse_proto_rawDescData []byte
)

func file_services_pix_response_pixClaimServiceResponse_proto_rawDescGZIP() []byte {
	file_services_pix_response_pixClaimServiceResponse_proto_rawDescOnce.Do(func() {
		file_services_pix_response_pixClaimServiceResponse_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_pix_response_pixClaimServiceResponse_proto_rawDesc), len(file_services_pix_response_pixClaimServiceResponse_proto_rawDesc)))
	})
	return file_services_pix_response_pixClaimServiceResponse_proto_rawDescData
}

var file_services_pix_response_pixClaimServiceResponse_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_services_pix_response_pixClaimServiceResponse_proto_goTypes = []any{
	(*PixKeyClaimResponse)(nil),    // 0: response.PixKeyClaimResponse
	(wallet.PixKeyCreateResult)(0), // 1: wallet.PixKeyCreateResult
	(wallet.PixKeyClaimStatus)(0),  // 2: wallet.PixKeyClaimStatus
	(*timestamppb.Timestamp)(nil),  // 3: google.protobuf.Timestamp
}
var file_services_pix_response_pixClaimServiceResponse_proto_depIdxs = []int32{
	1, // 0: response.PixKeyClaimResponse.result:type_name -> wallet.PixKeyCreateResult
	2, // 1: response.PixKeyClaimResponse.claim_status:type_name -> wallet.PixKeyClaimStatus
	3, // 2: response.PixKeyClaimResponse.claim_resolution_limit:type_name -> google.protobuf.Timestamp
	3, // 3: response.PixKeyClaimResponse.claim_conclusion_limit:type_name -> google.protobuf.Timestamp
	3, // 4: response.PixKeyClaimResponse.claim_last_modified_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_services_pix_response_pixClaimServiceResponse_proto_init() }
func file_services_pix_response_pixClaimServiceResponse_proto_init() {
	if File_services_pix_response_pixClaimServiceResponse_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_response_pixClaimServiceResponse_proto_rawDesc), len(file_services_pix_response_pixClaimServiceResponse_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_pix_response_pixClaimServiceResponse_proto_goTypes,
		DependencyIndexes: file_services_pix_response_pixClaimServiceResponse_proto_depIdxs,
		MessageInfos:      file_services_pix_response_pixClaimServiceResponse_proto_msgTypes,
	}.Build()
	File_services_pix_response_pixClaimServiceResponse_proto = out.File
	file_services_pix_response_pixClaimServiceResponse_proto_goTypes = nil
	file_services_pix_response_pixClaimServiceResponse_proto_depIdxs = nil
}
