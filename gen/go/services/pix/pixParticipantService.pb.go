// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: services/pix/pixParticipantService.proto

package pix

import (
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_services_pix_pixParticipantService_proto protoreflect.FileDescriptor

const file_services_pix_pixParticipantService_proto_rawDesc = "" +
	"\n" +
	"(services/pix/pixParticipantService.proto\x12\bservices\x1a:services/pix/request/pixParticipantServiceRequest.proto\x1a<services/pix/response/pixDictManagementServiceResponse.proto2\xcb\x02\n" +
	"\x1fPixParticipantManagementService\x12W\n" +
	"\x17PixParticipantPolicyGet\x12\x1c.request.PixKeyPolicyRequest\x1a\x1e.response.PixKeyPolicyResponse\x12i\n" +
	"\x1dPixParticipantPolicyStatusGet\x12\".request.PixKeyPolicyStatusRequest\x1a$.response.PixKeyPolicyStatusResponse\x12d\n" +
	"\x18PixParticipantBalanceGet\x12\".request.PixKeyPolicyStatusRequest\x1a$.response.PixKeyPolicyStatusResponseBEZCgitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pixb\x06proto3"

var file_services_pix_pixParticipantService_proto_goTypes = []any{
	(*request.PixKeyPolicyRequest)(nil),         // 0: request.PixKeyPolicyRequest
	(*request.PixKeyPolicyStatusRequest)(nil),   // 1: request.PixKeyPolicyStatusRequest
	(*response.PixKeyPolicyResponse)(nil),       // 2: response.PixKeyPolicyResponse
	(*response.PixKeyPolicyStatusResponse)(nil), // 3: response.PixKeyPolicyStatusResponse
}
var file_services_pix_pixParticipantService_proto_depIdxs = []int32{
	0, // 0: services.PixParticipantManagementService.PixParticipantPolicyGet:input_type -> request.PixKeyPolicyRequest
	1, // 1: services.PixParticipantManagementService.PixParticipantPolicyStatusGet:input_type -> request.PixKeyPolicyStatusRequest
	1, // 2: services.PixParticipantManagementService.PixParticipantBalanceGet:input_type -> request.PixKeyPolicyStatusRequest
	2, // 3: services.PixParticipantManagementService.PixParticipantPolicyGet:output_type -> response.PixKeyPolicyResponse
	3, // 4: services.PixParticipantManagementService.PixParticipantPolicyStatusGet:output_type -> response.PixKeyPolicyStatusResponse
	3, // 5: services.PixParticipantManagementService.PixParticipantBalanceGet:output_type -> response.PixKeyPolicyStatusResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_services_pix_pixParticipantService_proto_init() }
func file_services_pix_pixParticipantService_proto_init() {
	if File_services_pix_pixParticipantService_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_pix_pixParticipantService_proto_rawDesc), len(file_services_pix_pixParticipantService_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_pix_pixParticipantService_proto_goTypes,
		DependencyIndexes: file_services_pix_pixParticipantService_proto_depIdxs,
	}.Build()
	File_services_pix_pixParticipantService_proto = out.File
	file_services_pix_pixParticipantService_proto_goTypes = nil
	file_services_pix_pixParticipantService_proto_depIdxs = nil
}