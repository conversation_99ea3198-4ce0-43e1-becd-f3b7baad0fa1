// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pix/pixQRCodeService.proto

package pix

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixQRCodeService_QRCodeStaticCreate_FullMethodName     = "/services.PixQRCodeService/QRCodeStaticCreate"
	PixQRCodeService_QRCodeStaticGetByTxId_FullMethodName  = "/services.PixQRCodeService/QRCodeStaticGetByTxId"
	PixQRCodeService_QRCodeDynamicCreate_FullMethodName    = "/services.PixQRCodeService/QRCodeDynamicCreate"
	PixQRCodeService_QRCodeDynamicGetByTxId_FullMethodName = "/services.PixQRCodeService/QRCodeDynamicGetByTxId"
	PixQRCodeService_QRCodeGetByPayload_FullMethodName     = "/services.PixQRCodeService/QRCodeGetByPayload"
)

// PixQRCodeServiceClient is the client API for PixQRCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixQRCodeServiceClient interface {
	QRCodeStaticCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error)
	QRCodeStaticGetByTxId(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticDecodeResponse, error)
	QRCodeDynamicCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error)
	QRCodeDynamicGetByTxId(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error)
	QRCodeGetByPayload(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error)
}

type pixQRCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixQRCodeServiceClient(cc grpc.ClientConnInterface) PixQRCodeServiceClient {
	return &pixQRCodeServiceClient{cc}
}

func (c *pixQRCodeServiceClient) QRCodeStaticCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticCreateResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeStaticCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixQRCodeServiceClient) QRCodeStaticGetByTxId(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticDecodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticDecodeResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeStaticGetByTxId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixQRCodeServiceClient) QRCodeDynamicCreate(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticCreateResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeDynamicCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixQRCodeServiceClient) QRCodeDynamicGetByTxId(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticCreateResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeDynamicGetByTxId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixQRCodeServiceClient) QRCodeGetByPayload(ctx context.Context, in *request.QRCodeStaticCreateRequest, opts ...grpc.CallOption) (*response.QRCodeStaticCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.QRCodeStaticCreateResponse)
	err := c.cc.Invoke(ctx, PixQRCodeService_QRCodeGetByPayload_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixQRCodeServiceServer is the server API for PixQRCodeService service.
// All implementations must embed UnimplementedPixQRCodeServiceServer
// for forward compatibility.
type PixQRCodeServiceServer interface {
	QRCodeStaticCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error)
	QRCodeStaticGetByTxId(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticDecodeResponse, error)
	QRCodeDynamicCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error)
	QRCodeDynamicGetByTxId(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error)
	QRCodeGetByPayload(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error)
	mustEmbedUnimplementedPixQRCodeServiceServer()
}

// UnimplementedPixQRCodeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixQRCodeServiceServer struct{}

func (UnimplementedPixQRCodeServiceServer) QRCodeStaticCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeStaticCreate not implemented")
}
func (UnimplementedPixQRCodeServiceServer) QRCodeStaticGetByTxId(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticDecodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeStaticGetByTxId not implemented")
}
func (UnimplementedPixQRCodeServiceServer) QRCodeDynamicCreate(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeDynamicCreate not implemented")
}
func (UnimplementedPixQRCodeServiceServer) QRCodeDynamicGetByTxId(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeDynamicGetByTxId not implemented")
}
func (UnimplementedPixQRCodeServiceServer) QRCodeGetByPayload(context.Context, *request.QRCodeStaticCreateRequest) (*response.QRCodeStaticCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QRCodeGetByPayload not implemented")
}
func (UnimplementedPixQRCodeServiceServer) mustEmbedUnimplementedPixQRCodeServiceServer() {}
func (UnimplementedPixQRCodeServiceServer) testEmbeddedByValue()                          {}

// UnsafePixQRCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixQRCodeServiceServer will
// result in compilation errors.
type UnsafePixQRCodeServiceServer interface {
	mustEmbedUnimplementedPixQRCodeServiceServer()
}

func RegisterPixQRCodeServiceServer(s grpc.ServiceRegistrar, srv PixQRCodeServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixQRCodeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixQRCodeService_ServiceDesc, srv)
}

func _PixQRCodeService_QRCodeStaticCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeStaticCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeStaticCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeStaticCreate(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixQRCodeService_QRCodeStaticGetByTxId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeStaticGetByTxId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeStaticGetByTxId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeStaticGetByTxId(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixQRCodeService_QRCodeDynamicCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeDynamicCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeDynamicCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeDynamicCreate(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixQRCodeService_QRCodeDynamicGetByTxId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeDynamicGetByTxId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeDynamicGetByTxId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeDynamicGetByTxId(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixQRCodeService_QRCodeGetByPayload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.QRCodeStaticCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixQRCodeServiceServer).QRCodeGetByPayload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixQRCodeService_QRCodeGetByPayload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixQRCodeServiceServer).QRCodeGetByPayload(ctx, req.(*request.QRCodeStaticCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixQRCodeService_ServiceDesc is the grpc.ServiceDesc for PixQRCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixQRCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixQRCodeService",
	HandlerType: (*PixQRCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QRCodeStaticCreate",
			Handler:    _PixQRCodeService_QRCodeStaticCreate_Handler,
		},
		{
			MethodName: "QRCodeStaticGetByTxId",
			Handler:    _PixQRCodeService_QRCodeStaticGetByTxId_Handler,
		},
		{
			MethodName: "QRCodeDynamicCreate",
			Handler:    _PixQRCodeService_QRCodeDynamicCreate_Handler,
		},
		{
			MethodName: "QRCodeDynamicGetByTxId",
			Handler:    _PixQRCodeService_QRCodeDynamicGetByTxId_Handler,
		},
		{
			MethodName: "QRCodeGetByPayload",
			Handler:    _PixQRCodeService_QRCodeGetByPayload_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pix/pixQRCodeService.proto",
}
