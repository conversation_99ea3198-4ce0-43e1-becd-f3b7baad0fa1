// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: services/pix/pixParticipantService.proto

package pix

import (
	context "context"
	request "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	response "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PixParticipantManagementService_PixParticipantPolicyGet_FullMethodName       = "/services.PixParticipantManagementService/PixParticipantPolicyGet"
	PixParticipantManagementService_PixParticipantPolicyStatusGet_FullMethodName = "/services.PixParticipantManagementService/PixParticipantPolicyStatusGet"
	PixParticipantManagementService_PixParticipantBalanceGet_FullMethodName      = "/services.PixParticipantManagementService/PixParticipantBalanceGet"
)

// PixParticipantManagementServiceClient is the client API for PixParticipantManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PixParticipantManagementServiceClient interface {
	PixParticipantPolicyGet(ctx context.Context, in *request.PixKeyPolicyRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyResponse, error)
	PixParticipantPolicyStatusGet(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error)
	PixParticipantBalanceGet(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error)
}

type pixParticipantManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPixParticipantManagementServiceClient(cc grpc.ClientConnInterface) PixParticipantManagementServiceClient {
	return &pixParticipantManagementServiceClient{cc}
}

func (c *pixParticipantManagementServiceClient) PixParticipantPolicyGet(ctx context.Context, in *request.PixKeyPolicyRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyPolicyResponse)
	err := c.cc.Invoke(ctx, PixParticipantManagementService_PixParticipantPolicyGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixParticipantManagementServiceClient) PixParticipantPolicyStatusGet(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyPolicyStatusResponse)
	err := c.cc.Invoke(ctx, PixParticipantManagementService_PixParticipantPolicyStatusGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pixParticipantManagementServiceClient) PixParticipantBalanceGet(ctx context.Context, in *request.PixKeyPolicyStatusRequest, opts ...grpc.CallOption) (*response.PixKeyPolicyStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(response.PixKeyPolicyStatusResponse)
	err := c.cc.Invoke(ctx, PixParticipantManagementService_PixParticipantBalanceGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PixParticipantManagementServiceServer is the server API for PixParticipantManagementService service.
// All implementations must embed UnimplementedPixParticipantManagementServiceServer
// for forward compatibility.
type PixParticipantManagementServiceServer interface {
	PixParticipantPolicyGet(context.Context, *request.PixKeyPolicyRequest) (*response.PixKeyPolicyResponse, error)
	PixParticipantPolicyStatusGet(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error)
	PixParticipantBalanceGet(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error)
	mustEmbedUnimplementedPixParticipantManagementServiceServer()
}

// UnimplementedPixParticipantManagementServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPixParticipantManagementServiceServer struct{}

func (UnimplementedPixParticipantManagementServiceServer) PixParticipantPolicyGet(context.Context, *request.PixKeyPolicyRequest) (*response.PixKeyPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixParticipantPolicyGet not implemented")
}
func (UnimplementedPixParticipantManagementServiceServer) PixParticipantPolicyStatusGet(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixParticipantPolicyStatusGet not implemented")
}
func (UnimplementedPixParticipantManagementServiceServer) PixParticipantBalanceGet(context.Context, *request.PixKeyPolicyStatusRequest) (*response.PixKeyPolicyStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PixParticipantBalanceGet not implemented")
}
func (UnimplementedPixParticipantManagementServiceServer) mustEmbedUnimplementedPixParticipantManagementServiceServer() {
}
func (UnimplementedPixParticipantManagementServiceServer) testEmbeddedByValue() {}

// UnsafePixParticipantManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PixParticipantManagementServiceServer will
// result in compilation errors.
type UnsafePixParticipantManagementServiceServer interface {
	mustEmbedUnimplementedPixParticipantManagementServiceServer()
}

func RegisterPixParticipantManagementServiceServer(s grpc.ServiceRegistrar, srv PixParticipantManagementServiceServer) {
	// If the following call pancis, it indicates UnimplementedPixParticipantManagementServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PixParticipantManagementService_ServiceDesc, srv)
}

func _PixParticipantManagementService_PixParticipantPolicyGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixParticipantManagementServiceServer).PixParticipantPolicyGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixParticipantManagementService_PixParticipantPolicyGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixParticipantManagementServiceServer).PixParticipantPolicyGet(ctx, req.(*request.PixKeyPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixParticipantManagementService_PixParticipantPolicyStatusGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyPolicyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixParticipantManagementServiceServer).PixParticipantPolicyStatusGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixParticipantManagementService_PixParticipantPolicyStatusGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixParticipantManagementServiceServer).PixParticipantPolicyStatusGet(ctx, req.(*request.PixKeyPolicyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PixParticipantManagementService_PixParticipantBalanceGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(request.PixKeyPolicyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PixParticipantManagementServiceServer).PixParticipantBalanceGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PixParticipantManagementService_PixParticipantBalanceGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PixParticipantManagementServiceServer).PixParticipantBalanceGet(ctx, req.(*request.PixKeyPolicyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PixParticipantManagementService_ServiceDesc is the grpc.ServiceDesc for PixParticipantManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PixParticipantManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.PixParticipantManagementService",
	HandlerType: (*PixParticipantManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PixParticipantPolicyGet",
			Handler:    _PixParticipantManagementService_PixParticipantPolicyGet_Handler,
		},
		{
			MethodName: "PixParticipantPolicyStatusGet",
			Handler:    _PixParticipantManagementService_PixParticipantPolicyStatusGet_Handler,
		},
		{
			MethodName: "PixParticipantBalanceGet",
			Handler:    _PixParticipantManagementService_PixParticipantBalanceGet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/pix/pixParticipantService.proto",
}
