// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: wallet/pixKey.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CommonResult represents a generic operation outcome.
// It is typically used in RPC API responses to signal success
//   - when no specific error message or detailed failure information is provided.
type CommonResult int32

const (
	CommonResult_SUCCESS        CommonResult = 0
	CommonResult_INTERNAL_ERROR CommonResult = 1
)

// Enum value maps for CommonResult.
var (
	CommonResult_name = map[int32]string{
		0: "SUCCESS",
		1: "INTERNAL_ERROR",
	}
	CommonResult_value = map[string]int32{
		"SUCCESS":        0,
		"INTERNAL_ERROR": 1,
	}
)

func (x CommonResult) Enum() *CommonResult {
	p := new(CommonResult)
	*p = x
	return p
}

func (x CommonResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonResult) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[0].Descriptor()
}

func (CommonResult) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[0]
}

func (x CommonResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonResult.Descriptor instead.
func (CommonResult) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{0}
}

// PixKeyCreateResult defines the possible outcomes when attempting to create a Pix Key.
type PixKeyCreateResult int32

const (
	PixKeyCreateResult_CREATE_PIX_SUCCESS                            PixKeyCreateResult = 0 // The Pix Key was created successfully.
	PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON      PixKeyCreateResult = 1 // The Pix Key already exists and is owned by another person.
	PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT PixKeyCreateResult = 2 // The Pix Key already exists and is linked to another financial institution.
)

// Enum value maps for PixKeyCreateResult.
var (
	PixKeyCreateResult_name = map[int32]string{
		0: "CREATE_PIX_SUCCESS",
		1: "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON",
		2: "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT",
	}
	PixKeyCreateResult_value = map[string]int32{
		"CREATE_PIX_SUCCESS":                            0,
		"CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON":      1,
		"CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT": 2,
	}
)

func (x PixKeyCreateResult) Enum() *PixKeyCreateResult {
	p := new(PixKeyCreateResult)
	*p = x
	return p
}

func (x PixKeyCreateResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyCreateResult) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[1].Descriptor()
}

func (PixKeyCreateResult) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[1]
}

func (x PixKeyCreateResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyCreateResult.Descriptor instead.
func (PixKeyCreateResult) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{1}
}

// PixKeyType defines the valid types of Pix Keys.
type PixKeyType int32

const (
	PixKeyType_CPF   PixKeyType = 0 // Brazilian Individual Taxpayer Registry, max. 11 digits.
	PixKeyType_CNPJ  PixKeyType = 1 // Brazilian Company Taxpayer Registry, max. 14 digits.
	PixKeyType_EMAIL PixKeyType = 2 // Email address, max. 72 characters.
	PixKeyType_PHONE PixKeyType = 3 // Phone number, including country and area code (e.g., +5511987654321).
	PixKeyType_EVP   PixKeyType = 4 // EVP - Virtual Payment Address - a random key generated by the Pix system.
)

// Enum value maps for PixKeyType.
var (
	PixKeyType_name = map[int32]string{
		0: "CPF",
		1: "CNPJ",
		2: "EMAIL",
		3: "PHONE",
		4: "EVP",
	}
	PixKeyType_value = map[string]int32{
		"CPF":   0,
		"CNPJ":  1,
		"EMAIL": 2,
		"PHONE": 3,
		"EVP":   4,
	}
)

func (x PixKeyType) Enum() *PixKeyType {
	p := new(PixKeyType)
	*p = x
	return p
}

func (x PixKeyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[2].Descriptor()
}

func (PixKeyType) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[2]
}

func (x PixKeyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyType.Descriptor instead.
func (PixKeyType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{2}
}

// PixKeyCreateReason defines the reasons for creating a Pix Key.
type PixKeyCreateReason int32

const (
	PixKeyCreateReason_CREATE_PIXKEY_REQUEST        PixKeyCreateReason = 0 // The Pix Key creation was initiated by a client request.
	PixKeyCreateReason_CREATE_PIXKEY_RECONCILIATION PixKeyCreateReason = 6 // The Pix Key creation is part of a reconciliation process.
)

// Enum value maps for PixKeyCreateReason.
var (
	PixKeyCreateReason_name = map[int32]string{
		0: "CREATE_PIXKEY_REQUEST",
		6: "CREATE_PIXKEY_RECONCILIATION",
	}
	PixKeyCreateReason_value = map[string]int32{
		"CREATE_PIXKEY_REQUEST":        0,
		"CREATE_PIXKEY_RECONCILIATION": 6,
	}
)

func (x PixKeyCreateReason) Enum() *PixKeyCreateReason {
	p := new(PixKeyCreateReason)
	*p = x
	return p
}

func (x PixKeyCreateReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyCreateReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[3].Descriptor()
}

func (PixKeyCreateReason) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[3]
}

func (x PixKeyCreateReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyCreateReason.Descriptor instead.
func (PixKeyCreateReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{3}
}

// PixKeyUpdateReason defines the reasons for updating a Pix Key.
type PixKeyUpdateReason int32

const (
	PixKeyUpdateReason_UPDATE_PIXKEY_REQUEST         PixKeyUpdateReason = 0 // The Pix Key update was initiated by a client request.
	PixKeyUpdateReason_UPDATE_PIX_KEY_ACCOUNT_CHANGE PixKeyUpdateReason = 2 // The Pix Key is being updated due to a change in the client's associated account number.
	PixKeyUpdateReason_UPDATE_PIXKEY_RECONCILIATION  PixKeyUpdateReason = 6 // The Pix Key update is part of a reconciliation process.
)

// Enum value maps for PixKeyUpdateReason.
var (
	PixKeyUpdateReason_name = map[int32]string{
		0: "UPDATE_PIXKEY_REQUEST",
		2: "UPDATE_PIX_KEY_ACCOUNT_CHANGE",
		6: "UPDATE_PIXKEY_RECONCILIATION",
	}
	PixKeyUpdateReason_value = map[string]int32{
		"UPDATE_PIXKEY_REQUEST":         0,
		"UPDATE_PIX_KEY_ACCOUNT_CHANGE": 2,
		"UPDATE_PIXKEY_RECONCILIATION":  6,
	}
)

func (x PixKeyUpdateReason) Enum() *PixKeyUpdateReason {
	p := new(PixKeyUpdateReason)
	*p = x
	return p
}

func (x PixKeyUpdateReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyUpdateReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[4].Descriptor()
}

func (PixKeyUpdateReason) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[4]
}

func (x PixKeyUpdateReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyUpdateReason.Descriptor instead.
func (PixKeyUpdateReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{4}
}

// PixKeyDeleteReason defines the reasons for deleting a Pix Key.
type PixKeyDeleteReason int32

const (
	PixKeyDeleteReason_DELETE_CLIENT_REQUEST             PixKeyDeleteReason = 0 // The Pix Key deletion was initiated by a client request.
	PixKeyDeleteReason_DELETE_FRAUD                      PixKeyDeleteReason = 4 // The Pix Key was deleted due to detected fraudulent activity.
	PixKeyDeleteReason_DELETE_RECONCILIATION             PixKeyDeleteReason = 6 // The Pix Key deletion is part of a reconciliation process.
	PixKeyDeleteReason_DELETE_FEDERAL_REVENUE_VALIDATION PixKeyDeleteReason = 8 // The Pix Key was deleted due to a validation by a government authority (e.g., Federal Revenue).
)

// Enum value maps for PixKeyDeleteReason.
var (
	PixKeyDeleteReason_name = map[int32]string{
		0: "DELETE_CLIENT_REQUEST",
		4: "DELETE_FRAUD",
		6: "DELETE_RECONCILIATION",
		8: "DELETE_FEDERAL_REVENUE_VALIDATION",
	}
	PixKeyDeleteReason_value = map[string]int32{
		"DELETE_CLIENT_REQUEST":             0,
		"DELETE_FRAUD":                      4,
		"DELETE_RECONCILIATION":             6,
		"DELETE_FEDERAL_REVENUE_VALIDATION": 8,
	}
)

func (x PixKeyDeleteReason) Enum() *PixKeyDeleteReason {
	p := new(PixKeyDeleteReason)
	*p = x
	return p
}

func (x PixKeyDeleteReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyDeleteReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[5].Descriptor()
}

func (PixKeyDeleteReason) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[5]
}

func (x PixKeyDeleteReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyDeleteReason.Descriptor instead.
func (PixKeyDeleteReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{5}
}

// PixKeyClaimStatus defines the possible statuses for a Pix Key claim process.
type PixKeyClaimStatus int32

const (
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_OPEN      PixKeyClaimStatus = 0 // Status whenever a pixClaim task is created.
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_ACK       PixKeyClaimStatus = 1 // Current PixKey Owner ACK he has received the claim.
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_CONFIRMED PixKeyClaimStatus = 2 // Current PixKey Owner agrees to give the PixKey away
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_CANCELLED PixKeyClaimStatus = 3 // Either Requester or Owner cancel the claim
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_SUCCESS   PixKeyClaimStatus = 4 // PixKey Owner agrees to give, and requester confirms
)

// Enum value maps for PixKeyClaimStatus.
var (
	PixKeyClaimStatus_name = map[int32]string{
		0: "PIXKEY_CLAIM_STATUS_OPEN",
		1: "PIXKEY_CLAIM_STATUS_ACK",
		2: "PIXKEY_CLAIM_STATUS_CONFIRMED",
		3: "PIXKEY_CLAIM_STATUS_CANCELLED",
		4: "PIXKEY_CLAIM_STATUS_SUCCESS",
	}
	PixKeyClaimStatus_value = map[string]int32{
		"PIXKEY_CLAIM_STATUS_OPEN":      0,
		"PIXKEY_CLAIM_STATUS_ACK":       1,
		"PIXKEY_CLAIM_STATUS_CONFIRMED": 2,
		"PIXKEY_CLAIM_STATUS_CANCELLED": 3,
		"PIXKEY_CLAIM_STATUS_SUCCESS":   4,
	}
)

func (x PixKeyClaimStatus) Enum() *PixKeyClaimStatus {
	p := new(PixKeyClaimStatus)
	*p = x
	return p
}

func (x PixKeyClaimStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyClaimStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[6].Descriptor()
}

func (PixKeyClaimStatus) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[6]
}

func (x PixKeyClaimStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyClaimStatus.Descriptor instead.
func (PixKeyClaimStatus) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{6}
}

type PixKeyBucketPolicy int32

const (
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN           PixKeyBucketPolicy = 0
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN_V2        PixKeyBucketPolicy = 1
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_READ_PARTICIPANT_ANTISCAN    PixKeyBucketPolicy = 2
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_STATISTICS_READ              PixKeyBucketPolicy = 3
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_WRITE                        PixKeyBucketPolicy = 4
	PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_UPDATE                       PixKeyBucketPolicy = 5
	PixKeyBucketPolicy_PIXKEY_POLICY_CLAIMS_READ                          PixKeyBucketPolicy = 6
	PixKeyBucketPolicy_PIXKEY_POLICY_CLAIMS_WRITE                         PixKeyBucketPolicy = 7
	PixKeyBucketPolicy_PIXKEY_POLICY_CLAIMS_LIST_WITH_ROLE                PixKeyBucketPolicy = 8
	PixKeyBucketPolicy_PIXKEY_POLICY_CLAIMS_LIST_WITHOUT_ROLE             PixKeyBucketPolicy = 9
	PixKeyBucketPolicy_PIXKEY_POLICY_SYNC_VERIFICATIONS_WRITE             PixKeyBucketPolicy = 10
	PixKeyBucketPolicy_PIXKEY_POLICY_CIDS_FILES_WRITE                     PixKeyBucketPolicy = 11
	PixKeyBucketPolicy_PIXKEY_POLICY_CIDS_FILES_READ                      PixKeyBucketPolicy = 12
	PixKeyBucketPolicy_PIXKEY_POLICY_CIDS_EVENTS_LIST                     PixKeyBucketPolicy = 13
	PixKeyBucketPolicy_PIXKEY_POLICY_CIDS_ENTRIES_READ                    PixKeyBucketPolicy = 14
	PixKeyBucketPolicy_PIXKEY_POLICY_INFRACTION_REPORTS_READ              PixKeyBucketPolicy = 15
	PixKeyBucketPolicy_PIXKEY_POLICY_INFRACTION_REPORTS_WRITE             PixKeyBucketPolicy = 16
	PixKeyBucketPolicy_PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITH_ROLE    PixKeyBucketPolicy = 17
	PixKeyBucketPolicy_PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITHOUT_ROLE PixKeyBucketPolicy = 18
	PixKeyBucketPolicy_PIXKEY_POLICY_KEYS_CHECK                           PixKeyBucketPolicy = 19
	PixKeyBucketPolicy_PIXKEY_POLICY_REFUNDS_READ                         PixKeyBucketPolicy = 20
	PixKeyBucketPolicy_PIXKEY_POLICY_REFUNDS_WRITE                        PixKeyBucketPolicy = 21
	PixKeyBucketPolicy_PIXKEY_POLICY_REFUND_LIST_WITH_ROLE                PixKeyBucketPolicy = 22
	PixKeyBucketPolicy_PIXKEY_POLICY_REFUND_LIST_WITHOUT_ROLE             PixKeyBucketPolicy = 23
	PixKeyBucketPolicy_PIXKEY_POLICY_FRAUD_MARKERS_READ                   PixKeyBucketPolicy = 24
	PixKeyBucketPolicy_PIXKEY_POLICY_FRAUD_MARKERS_WRITE                  PixKeyBucketPolicy = 25
	PixKeyBucketPolicy_PIXKEY_POLICY_FRAUD_MARKERS_LIST                   PixKeyBucketPolicy = 26
	PixKeyBucketPolicy_PIXKEY_POLICY_PERSONS_STATISTICS_READ              PixKeyBucketPolicy = 27
	PixKeyBucketPolicy_PIXKEY_POLICY_POLICIES_READ                        PixKeyBucketPolicy = 28
	PixKeyBucketPolicy_PIXKEY_POLICY_POLICIES_LIST                        PixKeyBucketPolicy = 29
)

// Enum value maps for PixKeyBucketPolicy.
var (
	PixKeyBucketPolicy_name = map[int32]string{
		0:  "PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN",
		1:  "PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN_V2",
		2:  "PIXKEY_POLICY_ENTRIES_READ_PARTICIPANT_ANTISCAN",
		3:  "PIXKEY_POLICY_ENTRIES_STATISTICS_READ",
		4:  "PIXKEY_POLICY_ENTRIES_WRITE",
		5:  "PIXKEY_POLICY_ENTRIES_UPDATE",
		6:  "PIXKEY_POLICY_CLAIMS_READ",
		7:  "PIXKEY_POLICY_CLAIMS_WRITE",
		8:  "PIXKEY_POLICY_CLAIMS_LIST_WITH_ROLE",
		9:  "PIXKEY_POLICY_CLAIMS_LIST_WITHOUT_ROLE",
		10: "PIXKEY_POLICY_SYNC_VERIFICATIONS_WRITE",
		11: "PIXKEY_POLICY_CIDS_FILES_WRITE",
		12: "PIXKEY_POLICY_CIDS_FILES_READ",
		13: "PIXKEY_POLICY_CIDS_EVENTS_LIST",
		14: "PIXKEY_POLICY_CIDS_ENTRIES_READ",
		15: "PIXKEY_POLICY_INFRACTION_REPORTS_READ",
		16: "PIXKEY_POLICY_INFRACTION_REPORTS_WRITE",
		17: "PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITH_ROLE",
		18: "PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITHOUT_ROLE",
		19: "PIXKEY_POLICY_KEYS_CHECK",
		20: "PIXKEY_POLICY_REFUNDS_READ",
		21: "PIXKEY_POLICY_REFUNDS_WRITE",
		22: "PIXKEY_POLICY_REFUND_LIST_WITH_ROLE",
		23: "PIXKEY_POLICY_REFUND_LIST_WITHOUT_ROLE",
		24: "PIXKEY_POLICY_FRAUD_MARKERS_READ",
		25: "PIXKEY_POLICY_FRAUD_MARKERS_WRITE",
		26: "PIXKEY_POLICY_FRAUD_MARKERS_LIST",
		27: "PIXKEY_POLICY_PERSONS_STATISTICS_READ",
		28: "PIXKEY_POLICY_POLICIES_READ",
		29: "PIXKEY_POLICY_POLICIES_LIST",
	}
	PixKeyBucketPolicy_value = map[string]int32{
		"PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN":           0,
		"PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN_V2":        1,
		"PIXKEY_POLICY_ENTRIES_READ_PARTICIPANT_ANTISCAN":    2,
		"PIXKEY_POLICY_ENTRIES_STATISTICS_READ":              3,
		"PIXKEY_POLICY_ENTRIES_WRITE":                        4,
		"PIXKEY_POLICY_ENTRIES_UPDATE":                       5,
		"PIXKEY_POLICY_CLAIMS_READ":                          6,
		"PIXKEY_POLICY_CLAIMS_WRITE":                         7,
		"PIXKEY_POLICY_CLAIMS_LIST_WITH_ROLE":                8,
		"PIXKEY_POLICY_CLAIMS_LIST_WITHOUT_ROLE":             9,
		"PIXKEY_POLICY_SYNC_VERIFICATIONS_WRITE":             10,
		"PIXKEY_POLICY_CIDS_FILES_WRITE":                     11,
		"PIXKEY_POLICY_CIDS_FILES_READ":                      12,
		"PIXKEY_POLICY_CIDS_EVENTS_LIST":                     13,
		"PIXKEY_POLICY_CIDS_ENTRIES_READ":                    14,
		"PIXKEY_POLICY_INFRACTION_REPORTS_READ":              15,
		"PIXKEY_POLICY_INFRACTION_REPORTS_WRITE":             16,
		"PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITH_ROLE":    17,
		"PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITHOUT_ROLE": 18,
		"PIXKEY_POLICY_KEYS_CHECK":                           19,
		"PIXKEY_POLICY_REFUNDS_READ":                         20,
		"PIXKEY_POLICY_REFUNDS_WRITE":                        21,
		"PIXKEY_POLICY_REFUND_LIST_WITH_ROLE":                22,
		"PIXKEY_POLICY_REFUND_LIST_WITHOUT_ROLE":             23,
		"PIXKEY_POLICY_FRAUD_MARKERS_READ":                   24,
		"PIXKEY_POLICY_FRAUD_MARKERS_WRITE":                  25,
		"PIXKEY_POLICY_FRAUD_MARKERS_LIST":                   26,
		"PIXKEY_POLICY_PERSONS_STATISTICS_READ":              27,
		"PIXKEY_POLICY_POLICIES_READ":                        28,
		"PIXKEY_POLICY_POLICIES_LIST":                        29,
	}
)

func (x PixKeyBucketPolicy) Enum() *PixKeyBucketPolicy {
	p := new(PixKeyBucketPolicy)
	*p = x
	return p
}

func (x PixKeyBucketPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyBucketPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixKey_proto_enumTypes[7].Descriptor()
}

func (PixKeyBucketPolicy) Type() protoreflect.EnumType {
	return &file_wallet_pixKey_proto_enumTypes[7]
}

func (x PixKeyBucketPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyBucketPolicy.Descriptor instead.
func (PixKeyBucketPolicy) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{7}
}

// PixKey represents the fundamental structure of a Pix Key.
type PixKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyType       PixKeyType             `protobuf:"varint,1,opt,name=key_type,json=keyType,proto3,enum=wallet.PixKeyType" json:"key_type,omitempty"` // The type of the Pix Key (e.g., CPF, EMAIL).
	KeyValue      string                 `protobuf:"bytes,2,opt,name=key_value,json=keyValue,proto3" json:"key_value,omitempty"`                      // The actual value of the Pix Key (e.g., "123.456.789-00", "<EMAIL>").
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKey) Reset() {
	*x = PixKey{}
	mi := &file_wallet_pixKey_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKey) ProtoMessage() {}

func (x *PixKey) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_pixKey_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKey.ProtoReflect.Descriptor instead.
func (*PixKey) Descriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{0}
}

func (x *PixKey) GetKeyType() PixKeyType {
	if x != nil {
		return x.KeyType
	}
	return PixKeyType_CPF
}

func (x *PixKey) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

type PixKeyPolicy struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PolicyName         PixKeyBucketPolicy     `protobuf:"varint,1,opt,name=policy_name,json=policyName,proto3,enum=wallet.PixKeyBucketPolicy" json:"policy_name,omitempty"`
	TokenAvailable     int32                  `protobuf:"varint,2,opt,name=token_available,json=tokenAvailable,proto3" json:"token_available,omitempty"`
	TokenCapacity      int32                  `protobuf:"varint,3,opt,name=token_capacity,json=tokenCapacity,proto3" json:"token_capacity,omitempty"`
	TokenRefreshAmount int32                  `protobuf:"varint,4,opt,name=token_refresh_amount,json=tokenRefreshAmount,proto3" json:"token_refresh_amount,omitempty"`
	TokenRefreshTime   int32                  `protobuf:"varint,5,opt,name=token_refresh_time,json=tokenRefreshTime,proto3" json:"token_refresh_time,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PixKeyPolicy) Reset() {
	*x = PixKeyPolicy{}
	mi := &file_wallet_pixKey_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKeyPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKeyPolicy) ProtoMessage() {}

func (x *PixKeyPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_pixKey_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKeyPolicy.ProtoReflect.Descriptor instead.
func (*PixKeyPolicy) Descriptor() ([]byte, []int) {
	return file_wallet_pixKey_proto_rawDescGZIP(), []int{1}
}

func (x *PixKeyPolicy) GetPolicyName() PixKeyBucketPolicy {
	if x != nil {
		return x.PolicyName
	}
	return PixKeyBucketPolicy_PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN
}

func (x *PixKeyPolicy) GetTokenAvailable() int32 {
	if x != nil {
		return x.TokenAvailable
	}
	return 0
}

func (x *PixKeyPolicy) GetTokenCapacity() int32 {
	if x != nil {
		return x.TokenCapacity
	}
	return 0
}

func (x *PixKeyPolicy) GetTokenRefreshAmount() int32 {
	if x != nil {
		return x.TokenRefreshAmount
	}
	return 0
}

func (x *PixKeyPolicy) GetTokenRefreshTime() int32 {
	if x != nil {
		return x.TokenRefreshTime
	}
	return 0
}

var File_wallet_pixKey_proto protoreflect.FileDescriptor

const file_wallet_pixKey_proto_rawDesc = "" +
	"\n" +
	"\x13wallet/pixKey.proto\x12\x06wallet\"T\n" +
	"\x06PixKey\x12-\n" +
	"\bkey_type\x18\x01 \x01(\x0e2\x12.wallet.PixKeyTypeR\akeyType\x12\x1b\n" +
	"\tkey_value\x18\x02 \x01(\tR\bkeyValue\"\xfb\x01\n" +
	"\fPixKeyPolicy\x12;\n" +
	"\vpolicy_name\x18\x01 \x01(\x0e2\x1a.wallet.PixKeyBucketPolicyR\n" +
	"policyName\x12'\n" +
	"\x0ftoken_available\x18\x02 \x01(\x05R\x0etokenAvailable\x12%\n" +
	"\x0etoken_capacity\x18\x03 \x01(\x05R\rtokenCapacity\x120\n" +
	"\x14token_refresh_amount\x18\x04 \x01(\x05R\x12tokenRefreshAmount\x12,\n" +
	"\x12token_refresh_time\x18\x05 \x01(\x05R\x10tokenRefreshTime*/\n" +
	"\fCommonResult\x12\v\n" +
	"\aSUCCESS\x10\x00\x12\x12\n" +
	"\x0eINTERNAL_ERROR\x10\x01*\x8d\x01\n" +
	"\x12PixKeyCreateResult\x12\x16\n" +
	"\x12CREATE_PIX_SUCCESS\x10\x00\x12,\n" +
	"(CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON\x10\x01\x121\n" +
	"-CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT\x10\x02*>\n" +
	"\n" +
	"PixKeyType\x12\a\n" +
	"\x03CPF\x10\x00\x12\b\n" +
	"\x04CNPJ\x10\x01\x12\t\n" +
	"\x05EMAIL\x10\x02\x12\t\n" +
	"\x05PHONE\x10\x03\x12\a\n" +
	"\x03EVP\x10\x04*Q\n" +
	"\x12PixKeyCreateReason\x12\x19\n" +
	"\x15CREATE_PIXKEY_REQUEST\x10\x00\x12 \n" +
	"\x1cCREATE_PIXKEY_RECONCILIATION\x10\x06*t\n" +
	"\x12PixKeyUpdateReason\x12\x19\n" +
	"\x15UPDATE_PIXKEY_REQUEST\x10\x00\x12!\n" +
	"\x1dUPDATE_PIX_KEY_ACCOUNT_CHANGE\x10\x02\x12 \n" +
	"\x1cUPDATE_PIXKEY_RECONCILIATION\x10\x06*\x83\x01\n" +
	"\x12PixKeyDeleteReason\x12\x19\n" +
	"\x15DELETE_CLIENT_REQUEST\x10\x00\x12\x10\n" +
	"\fDELETE_FRAUD\x10\x04\x12\x19\n" +
	"\x15DELETE_RECONCILIATION\x10\x06\x12%\n" +
	"!DELETE_FEDERAL_REVENUE_VALIDATION\x10\b*\xb5\x01\n" +
	"\x11PixKeyClaimStatus\x12\x1c\n" +
	"\x18PIXKEY_CLAIM_STATUS_OPEN\x10\x00\x12\x1b\n" +
	"\x17PIXKEY_CLAIM_STATUS_ACK\x10\x01\x12!\n" +
	"\x1dPIXKEY_CLAIM_STATUS_CONFIRMED\x10\x02\x12!\n" +
	"\x1dPIXKEY_CLAIM_STATUS_CANCELLED\x10\x03\x12\x1f\n" +
	"\x1bPIXKEY_CLAIM_STATUS_SUCCESS\x10\x04*\xbe\t\n" +
	"\x12PixKeyBucketPolicy\x12,\n" +
	"(PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN\x10\x00\x12/\n" +
	"+PIXKEY_POLICY_ENTRIES_READ_USER_ANTISCAN_V2\x10\x01\x123\n" +
	"/PIXKEY_POLICY_ENTRIES_READ_PARTICIPANT_ANTISCAN\x10\x02\x12)\n" +
	"%PIXKEY_POLICY_ENTRIES_STATISTICS_READ\x10\x03\x12\x1f\n" +
	"\x1bPIXKEY_POLICY_ENTRIES_WRITE\x10\x04\x12 \n" +
	"\x1cPIXKEY_POLICY_ENTRIES_UPDATE\x10\x05\x12\x1d\n" +
	"\x19PIXKEY_POLICY_CLAIMS_READ\x10\x06\x12\x1e\n" +
	"\x1aPIXKEY_POLICY_CLAIMS_WRITE\x10\a\x12'\n" +
	"#PIXKEY_POLICY_CLAIMS_LIST_WITH_ROLE\x10\b\x12*\n" +
	"&PIXKEY_POLICY_CLAIMS_LIST_WITHOUT_ROLE\x10\t\x12*\n" +
	"&PIXKEY_POLICY_SYNC_VERIFICATIONS_WRITE\x10\n" +
	"\x12\"\n" +
	"\x1ePIXKEY_POLICY_CIDS_FILES_WRITE\x10\v\x12!\n" +
	"\x1dPIXKEY_POLICY_CIDS_FILES_READ\x10\f\x12\"\n" +
	"\x1ePIXKEY_POLICY_CIDS_EVENTS_LIST\x10\r\x12#\n" +
	"\x1fPIXKEY_POLICY_CIDS_ENTRIES_READ\x10\x0e\x12)\n" +
	"%PIXKEY_POLICY_INFRACTION_REPORTS_READ\x10\x0f\x12*\n" +
	"&PIXKEY_POLICY_INFRACTION_REPORTS_WRITE\x10\x10\x123\n" +
	"/PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITH_ROLE\x10\x11\x126\n" +
	"2PIXKEY_POLICY_INFRACTION_REPORTS_LIST_WITHOUT_ROLE\x10\x12\x12\x1c\n" +
	"\x18PIXKEY_POLICY_KEYS_CHECK\x10\x13\x12\x1e\n" +
	"\x1aPIXKEY_POLICY_REFUNDS_READ\x10\x14\x12\x1f\n" +
	"\x1bPIXKEY_POLICY_REFUNDS_WRITE\x10\x15\x12'\n" +
	"#PIXKEY_POLICY_REFUND_LIST_WITH_ROLE\x10\x16\x12*\n" +
	"&PIXKEY_POLICY_REFUND_LIST_WITHOUT_ROLE\x10\x17\x12$\n" +
	" PIXKEY_POLICY_FRAUD_MARKERS_READ\x10\x18\x12%\n" +
	"!PIXKEY_POLICY_FRAUD_MARKERS_WRITE\x10\x19\x12$\n" +
	" PIXKEY_POLICY_FRAUD_MARKERS_LIST\x10\x1a\x12)\n" +
	"%PIXKEY_POLICY_PERSONS_STATISTICS_READ\x10\x1b\x12\x1f\n" +
	"\x1bPIXKEY_POLICY_POLICIES_READ\x10\x1c\x12\x1f\n" +
	"\x1bPIXKEY_POLICY_POLICIES_LIST\x10\x1dB?Z=gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/walletb\x06proto3"

var (
	file_wallet_pixKey_proto_rawDescOnce sync.Once
	file_wallet_pixKey_proto_rawDescData []byte
)

func file_wallet_pixKey_proto_rawDescGZIP() []byte {
	file_wallet_pixKey_proto_rawDescOnce.Do(func() {
		file_wallet_pixKey_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_wallet_pixKey_proto_rawDesc), len(file_wallet_pixKey_proto_rawDesc)))
	})
	return file_wallet_pixKey_proto_rawDescData
}

var file_wallet_pixKey_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_wallet_pixKey_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_wallet_pixKey_proto_goTypes = []any{
	(CommonResult)(0),       // 0: wallet.CommonResult
	(PixKeyCreateResult)(0), // 1: wallet.PixKeyCreateResult
	(PixKeyType)(0),         // 2: wallet.PixKeyType
	(PixKeyCreateReason)(0), // 3: wallet.PixKeyCreateReason
	(PixKeyUpdateReason)(0), // 4: wallet.PixKeyUpdateReason
	(PixKeyDeleteReason)(0), // 5: wallet.PixKeyDeleteReason
	(PixKeyClaimStatus)(0),  // 6: wallet.PixKeyClaimStatus
	(PixKeyBucketPolicy)(0), // 7: wallet.PixKeyBucketPolicy
	(*PixKey)(nil),          // 8: wallet.PixKey
	(*PixKeyPolicy)(nil),    // 9: wallet.PixKeyPolicy
}
var file_wallet_pixKey_proto_depIdxs = []int32{
	2, // 0: wallet.PixKey.key_type:type_name -> wallet.PixKeyType
	7, // 1: wallet.PixKeyPolicy.policy_name:type_name -> wallet.PixKeyBucketPolicy
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_wallet_pixKey_proto_init() }
func file_wallet_pixKey_proto_init() {
	if File_wallet_pixKey_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_wallet_pixKey_proto_rawDesc), len(file_wallet_pixKey_proto_rawDesc)),
			NumEnums:      8,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wallet_pixKey_proto_goTypes,
		DependencyIndexes: file_wallet_pixKey_proto_depIdxs,
		EnumInfos:         file_wallet_pixKey_proto_enumTypes,
		MessageInfos:      file_wallet_pixKey_proto_msgTypes,
	}.Build()
	File_wallet_pixKey_proto = out.File
	file_wallet_pixKey_proto_goTypes = nil
	file_wallet_pixKey_proto_depIdxs = nil
}
