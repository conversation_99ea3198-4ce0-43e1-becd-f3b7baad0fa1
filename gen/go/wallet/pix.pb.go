// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: wallet/pixKey.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CommonResult represents a generic operation outcome.
// It is typically used in RPC API responses to signal success
//   - when no specific error message or detailed failure information is provided.
type CommonResult int32

const (
	CommonResult_SUCCESS        CommonResult = 0
	CommonResult_INTERNAL_ERROR CommonResult = 1
)

// Enum value maps for CommonResult.
var (
	CommonResult_name = map[int32]string{
		0: "SUCCESS",
		1: "INTERNAL_ERROR",
	}
	CommonResult_value = map[string]int32{
		"SUCCESS":        0,
		"INTERNAL_ERROR": 1,
	}
)

func (x CommonResult) Enum() *CommonResult {
	p := new(CommonResult)
	*p = x
	return p
}

func (x CommonResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonResult) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[0].Descriptor()
}

func (CommonResult) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[0]
}

func (x CommonResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonResult.Descriptor instead.
func (CommonResult) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{0}
}

// PixKeyCreateResult defines the possible outcomes when attempting to create a Pix Key.
type PixKeyCreateResult int32

const (
	PixKeyCreateResult_CREATE_PIX_SUCCESS                            PixKeyCreateResult = 0 // The Pix Key was created successfully.
	PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON      PixKeyCreateResult = 1 // The Pix Key already exists and is owned by another person.
	PixKeyCreateResult_CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT PixKeyCreateResult = 2 // The Pix Key already exists and is linked to another financial institution.
)

// Enum value maps for PixKeyCreateResult.
var (
	PixKeyCreateResult_name = map[int32]string{
		0: "CREATE_PIX_SUCCESS",
		1: "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON",
		2: "CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT",
	}
	PixKeyCreateResult_value = map[string]int32{
		"CREATE_PIX_SUCCESS":                            0,
		"CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON":      1,
		"CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT": 2,
	}
)

func (x PixKeyCreateResult) Enum() *PixKeyCreateResult {
	p := new(PixKeyCreateResult)
	*p = x
	return p
}

func (x PixKeyCreateResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyCreateResult) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[1].Descriptor()
}

func (PixKeyCreateResult) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[1]
}

func (x PixKeyCreateResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyCreateResult.Descriptor instead.
func (PixKeyCreateResult) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{1}
}

// PixKeyType defines the valid types of Pix Keys.
type PixKeyType int32

const (
	PixKeyType_CPF   PixKeyType = 0 // Brazilian Individual Taxpayer Registry, max. 11 digits.
	PixKeyType_CNPJ  PixKeyType = 1 // Brazilian Company Taxpayer Registry, max. 14 digits.
	PixKeyType_EMAIL PixKeyType = 2 // Email address, max. 72 characters.
	PixKeyType_PHONE PixKeyType = 3 // Phone number, including country and area code (e.g., +5511987654321).
	PixKeyType_EVP   PixKeyType = 4 // EVP - Virtual Payment Address - a random key generated by the Pix system.
)

// Enum value maps for PixKeyType.
var (
	PixKeyType_name = map[int32]string{
		0: "CPF",
		1: "CNPJ",
		2: "EMAIL",
		3: "PHONE",
		4: "EVP",
	}
	PixKeyType_value = map[string]int32{
		"CPF":   0,
		"CNPJ":  1,
		"EMAIL": 2,
		"PHONE": 3,
		"EVP":   4,
	}
)

func (x PixKeyType) Enum() *PixKeyType {
	p := new(PixKeyType)
	*p = x
	return p
}

func (x PixKeyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[2].Descriptor()
}

func (PixKeyType) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[2]
}

func (x PixKeyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyType.Descriptor instead.
func (PixKeyType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{2}
}

// PixKeyCreateReason defines the reasons for creating a Pix Key.
type PixKeyCreateReason int32

const (
	PixKeyCreateReason_CREATE_PIXKEY_REQUEST        PixKeyCreateReason = 0 // The Pix Key creation was initiated by a client request.
	PixKeyCreateReason_CREATE_PIXKEY_RECONCILIATION PixKeyCreateReason = 6 // The Pix Key creation is part of a reconciliation process.
)

// Enum value maps for PixKeyCreateReason.
var (
	PixKeyCreateReason_name = map[int32]string{
		0: "CREATE_PIXKEY_REQUEST",
		6: "CREATE_PIXKEY_RECONCILIATION",
	}
	PixKeyCreateReason_value = map[string]int32{
		"CREATE_PIXKEY_REQUEST":        0,
		"CREATE_PIXKEY_RECONCILIATION": 6,
	}
)

func (x PixKeyCreateReason) Enum() *PixKeyCreateReason {
	p := new(PixKeyCreateReason)
	*p = x
	return p
}

func (x PixKeyCreateReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyCreateReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[3].Descriptor()
}

func (PixKeyCreateReason) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[3]
}

func (x PixKeyCreateReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyCreateReason.Descriptor instead.
func (PixKeyCreateReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{3}
}

// PixKeyUpdateReason defines the reasons for updating a Pix Key.
type PixKeyUpdateReason int32

const (
	PixKeyUpdateReason_UPDATE_PIXKEY_REQUEST         PixKeyUpdateReason = 0 // The Pix Key update was initiated by a client request.
	PixKeyUpdateReason_UPDATE_PIX_KEY_ACCOUNT_CHANGE PixKeyUpdateReason = 2 // The Pix Key is being updated due to a change in the client's associated account number.
	PixKeyUpdateReason_UPDATE_PIXKEY_RECONCILIATION  PixKeyUpdateReason = 6 // The Pix Key update is part of a reconciliation process.
)

// Enum value maps for PixKeyUpdateReason.
var (
	PixKeyUpdateReason_name = map[int32]string{
		0: "UPDATE_PIXKEY_REQUEST",
		2: "UPDATE_PIX_KEY_ACCOUNT_CHANGE",
		6: "UPDATE_PIXKEY_RECONCILIATION",
	}
	PixKeyUpdateReason_value = map[string]int32{
		"UPDATE_PIXKEY_REQUEST":         0,
		"UPDATE_PIX_KEY_ACCOUNT_CHANGE": 2,
		"UPDATE_PIXKEY_RECONCILIATION":  6,
	}
)

func (x PixKeyUpdateReason) Enum() *PixKeyUpdateReason {
	p := new(PixKeyUpdateReason)
	*p = x
	return p
}

func (x PixKeyUpdateReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyUpdateReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[4].Descriptor()
}

func (PixKeyUpdateReason) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[4]
}

func (x PixKeyUpdateReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyUpdateReason.Descriptor instead.
func (PixKeyUpdateReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{4}
}

// PixKeyDeleteReason defines the reasons for deleting a Pix Key.
type PixKeyDeleteReason int32

const (
	PixKeyDeleteReason_DELETE_CLIENT_REQUEST             PixKeyDeleteReason = 0 // The Pix Key deletion was initiated by a client request.
	PixKeyDeleteReason_DELETE_FRAUD                      PixKeyDeleteReason = 4 // The Pix Key was deleted due to detected fraudulent activity.
	PixKeyDeleteReason_DELETE_RECONCILIATION             PixKeyDeleteReason = 6 // The Pix Key deletion is part of a reconciliation process.
	PixKeyDeleteReason_DELETE_FEDERAL_REVENUE_VALIDATION PixKeyDeleteReason = 8 // The Pix Key was deleted due to a validation by a government authority (e.g., Federal Revenue).
)

// Enum value maps for PixKeyDeleteReason.
var (
	PixKeyDeleteReason_name = map[int32]string{
		0: "DELETE_CLIENT_REQUEST",
		4: "DELETE_FRAUD",
		6: "DELETE_RECONCILIATION",
		8: "DELETE_FEDERAL_REVENUE_VALIDATION",
	}
	PixKeyDeleteReason_value = map[string]int32{
		"DELETE_CLIENT_REQUEST":             0,
		"DELETE_FRAUD":                      4,
		"DELETE_RECONCILIATION":             6,
		"DELETE_FEDERAL_REVENUE_VALIDATION": 8,
	}
)

func (x PixKeyDeleteReason) Enum() *PixKeyDeleteReason {
	p := new(PixKeyDeleteReason)
	*p = x
	return p
}

func (x PixKeyDeleteReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyDeleteReason) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[5].Descriptor()
}

func (PixKeyDeleteReason) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[5]
}

func (x PixKeyDeleteReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyDeleteReason.Descriptor instead.
func (PixKeyDeleteReason) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{5}
}

// PixKeyClaimStatus defines the possible statuses for a Pix Key claim process.
type PixKeyClaimStatus int32

const (
	PixKeyClaimStatus_PIXKEY_CLAIM_STATUS_OPEN PixKeyClaimStatus = 0 // The Pix Key claim process is open and pending resolution.
)

// Enum value maps for PixKeyClaimStatus.
var (
	PixKeyClaimStatus_name = map[int32]string{
		0: "PIXKEY_CLAIM_STATUS_OPEN",
	}
	PixKeyClaimStatus_value = map[string]int32{
		"PIXKEY_CLAIM_STATUS_OPEN": 0,
	}
)

func (x PixKeyClaimStatus) Enum() *PixKeyClaimStatus {
	p := new(PixKeyClaimStatus)
	*p = x
	return p
}

func (x PixKeyClaimStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PixKeyClaimStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pix_proto_enumTypes[6].Descriptor()
}

func (PixKeyClaimStatus) Type() protoreflect.EnumType {
	return &file_wallet_pix_proto_enumTypes[6]
}

func (x PixKeyClaimStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PixKeyClaimStatus.Descriptor instead.
func (PixKeyClaimStatus) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{6}
}

// PixKey represents the fundamental structure of a Pix Key.
type PixKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyType       PixKeyType             `protobuf:"varint,1,opt,name=key_type,json=keyType,proto3,enum=wallet.PixKeyType" json:"key_type,omitempty"` // The type of the Pix Key (e.g., CPF, EMAIL).
	KeyValue      string                 `protobuf:"bytes,2,opt,name=key_value,json=keyValue,proto3" json:"key_value,omitempty"`                      // The actual value of the Pix Key (e.g., "123.456.789-00", "<EMAIL>").
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PixKey) Reset() {
	*x = PixKey{}
	mi := &file_wallet_pix_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PixKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PixKey) ProtoMessage() {}

func (x *PixKey) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_pix_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PixKey.ProtoReflect.Descriptor instead.
func (*PixKey) Descriptor() ([]byte, []int) {
	return file_wallet_pix_proto_rawDescGZIP(), []int{0}
}

func (x *PixKey) GetKeyType() PixKeyType {
	if x != nil {
		return x.KeyType
	}
	return PixKeyType_CPF
}

func (x *PixKey) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

var File_wallet_pix_proto protoreflect.FileDescriptor

const file_wallet_pix_proto_rawDesc = "" +
	"\n" +
	"\x10wallet/pixKey.proto\x12\x06wallet\"T\n" +
	"\x06PixKey\x12-\n" +
	"\bkey_type\x18\x01 \x01(\x0e2\x12.wallet.PixKeyTypeR\akeyType\x12\x1b\n" +
	"\tkey_value\x18\x02 \x01(\tR\bkeyValue*/\n" +
	"\fCommonResult\x12\v\n" +
	"\aSUCCESS\x10\x00\x12\x12\n" +
	"\x0eINTERNAL_ERROR\x10\x01*\x8d\x01\n" +
	"\x12PixKeyCreateResult\x12\x16\n" +
	"\x12CREATE_PIX_SUCCESS\x10\x00\x12,\n" +
	"(CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PERSON\x10\x01\x121\n" +
	"-CREATE_PIX_ERROR_KEY_EXISTS_OTHER_PARTICIPANT\x10\x02*>\n" +
	"\n" +
	"PixKeyType\x12\a\n" +
	"\x03CPF\x10\x00\x12\b\n" +
	"\x04CNPJ\x10\x01\x12\t\n" +
	"\x05EMAIL\x10\x02\x12\t\n" +
	"\x05PHONE\x10\x03\x12\a\n" +
	"\x03EVP\x10\x04*Q\n" +
	"\x12PixKeyCreateReason\x12\x19\n" +
	"\x15CREATE_PIXKEY_REQUEST\x10\x00\x12 \n" +
	"\x1cCREATE_PIXKEY_RECONCILIATION\x10\x06*t\n" +
	"\x12PixKeyUpdateReason\x12\x19\n" +
	"\x15UPDATE_PIXKEY_REQUEST\x10\x00\x12!\n" +
	"\x1dUPDATE_PIX_KEY_ACCOUNT_CHANGE\x10\x02\x12 \n" +
	"\x1cUPDATE_PIXKEY_RECONCILIATION\x10\x06*\x83\x01\n" +
	"\x12PixKeyDeleteReason\x12\x19\n" +
	"\x15DELETE_CLIENT_REQUEST\x10\x00\x12\x10\n" +
	"\fDELETE_FRAUD\x10\x04\x12\x19\n" +
	"\x15DELETE_RECONCILIATION\x10\x06\x12%\n" +
	"!DELETE_FEDERAL_REVENUE_VALIDATION\x10\b*1\n" +
	"\x11PixKeyClaimStatus\x12\x1c\n" +
	"\x18PIXKEY_CLAIM_STATUS_OPEN\x10\x00B?Z=gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/walletb\x06proto3"

var (
	file_wallet_pix_proto_rawDescOnce sync.Once
	file_wallet_pix_proto_rawDescData []byte
)

func file_wallet_pix_proto_rawDescGZIP() []byte {
	file_wallet_pix_proto_rawDescOnce.Do(func() {
		file_wallet_pix_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_wallet_pix_proto_rawDesc), len(file_wallet_pix_proto_rawDesc)))
	})
	return file_wallet_pix_proto_rawDescData
}

var file_wallet_pix_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_wallet_pix_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_wallet_pix_proto_goTypes = []any{
	(CommonResult)(0),       // 0: wallet.CommonResult
	(PixKeyCreateResult)(0), // 1: wallet.PixKeyCreateResult
	(PixKeyType)(0),         // 2: wallet.PixKeyType
	(PixKeyCreateReason)(0), // 3: wallet.PixKeyCreateReason
	(PixKeyUpdateReason)(0), // 4: wallet.PixKeyUpdateReason
	(PixKeyDeleteReason)(0), // 5: wallet.PixKeyDeleteReason
	(PixKeyClaimStatus)(0),  // 6: wallet.PixKeyClaimStatus
	(*PixKey)(nil),          // 7: wallet.PixKey
}
var file_wallet_pix_proto_depIdxs = []int32{
	2, // 0: wallet.PixKey.key_type:type_name -> wallet.PixKeyType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_wallet_pix_proto_init() }
func file_wallet_pix_proto_init() {
	if File_wallet_pix_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_wallet_pix_proto_rawDesc), len(file_wallet_pix_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wallet_pix_proto_goTypes,
		DependencyIndexes: file_wallet_pix_proto_depIdxs,
		EnumInfos:         file_wallet_pix_proto_enumTypes,
		MessageInfos:      file_wallet_pix_proto_msgTypes,
	}.Build()
	File_wallet_pix_proto = out.File
	file_wallet_pix_proto_goTypes = nil
	file_wallet_pix_proto_depIdxs = nil
}