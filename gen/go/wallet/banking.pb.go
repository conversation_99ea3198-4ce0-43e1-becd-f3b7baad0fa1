// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: wallet/banking.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccountHolderTypeType int32

const (
	AccountHolderTypeType_NATURAL AccountHolderTypeType = 0 // Brazilian Individual Taxpayer Registry, max. 11 digits
	AccountHolderTypeType_LEGAL   AccountHolderTypeType = 1
)

// Enum value maps for AccountHolderTypeType.
var (
	AccountHolderTypeType_name = map[int32]string{
		0: "NATURAL",
		1: "LEGAL",
	}
	AccountHolderTypeType_value = map[string]int32{
		"NATURAL": 0,
		"LEGAL":   1,
	}
)

func (x AccountHolderTypeType) Enum() *AccountHolderTypeType {
	p := new(AccountHolderTypeType)
	*p = x
	return p
}

func (x AccountHolderTypeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountHolderTypeType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_banking_proto_enumTypes[0].Descriptor()
}

func (AccountHolderTypeType) Type() protoreflect.EnumType {
	return &file_wallet_banking_proto_enumTypes[0]
}

func (x AccountHolderTypeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountHolderTypeType.Descriptor instead.
func (AccountHolderTypeType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_banking_proto_rawDescGZIP(), []int{0}
}

type BankAccountType int32

const (
	BankAccountType_CHECKING_ACCOUNT BankAccountType = 0 // Used for day-to-day transactions.
	BankAccountType_SALARY_ACCOUNT   BankAccountType = 1 // Specifically for receiving salary payments.
	BankAccountType_SAVINGS_ACCOUNT  BankAccountType = 2 // Used for saving money, typically with lower transaction fees.
	BankAccountType_PAYMENT_ACCOUNT  BankAccountType = 3 // Often associated with digital wallets and fintechs.
)

// Enum value maps for BankAccountType.
var (
	BankAccountType_name = map[int32]string{
		0: "CHECKING_ACCOUNT",
		1: "SALARY_ACCOUNT",
		2: "SAVINGS_ACCOUNT",
		3: "PAYMENT_ACCOUNT",
	}
	BankAccountType_value = map[string]int32{
		"CHECKING_ACCOUNT": 0,
		"SALARY_ACCOUNT":   1,
		"SAVINGS_ACCOUNT":  2,
		"PAYMENT_ACCOUNT":  3,
	}
)

func (x BankAccountType) Enum() *BankAccountType {
	p := new(BankAccountType)
	*p = x
	return p
}

func (x BankAccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankAccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_banking_proto_enumTypes[1].Descriptor()
}

func (BankAccountType) Type() protoreflect.EnumType {
	return &file_wallet_banking_proto_enumTypes[1]
}

func (x BankAccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankAccountType.Descriptor instead.
func (BankAccountType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_banking_proto_rawDescGZIP(), []int{1}
}

type BankAccount struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Ispb                   int32                  `protobuf:"varint,1,opt,name=ispb,proto3" json:"ispb,omitempty"`
	BranchCode             string                 `protobuf:"bytes,2,opt,name=branch_code,json=branchCode,proto3" json:"branch_code,omitempty"` // The branch code of the bank account.
	AccountNumber          string                 `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	AccountType            BankAccountType        `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=wallet.BankAccountType" json:"account_type,omitempty"`
	Status                 string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	AccountOpeningDatetime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=account_opening_datetime,json=accountOpeningDatetime,proto3" json:"account_opening_datetime,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *BankAccount) Reset() {
	*x = BankAccount{}
	mi := &file_wallet_banking_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccount) ProtoMessage() {}

func (x *BankAccount) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_banking_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccount.ProtoReflect.Descriptor instead.
func (*BankAccount) Descriptor() ([]byte, []int) {
	return file_wallet_banking_proto_rawDescGZIP(), []int{0}
}

func (x *BankAccount) GetIspb() int32 {
	if x != nil {
		return x.Ispb
	}
	return 0
}

func (x *BankAccount) GetBranchCode() string {
	if x != nil {
		return x.BranchCode
	}
	return ""
}

func (x *BankAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankAccount) GetAccountType() BankAccountType {
	if x != nil {
		return x.AccountType
	}
	return BankAccountType_CHECKING_ACCOUNT
}

func (x *BankAccount) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BankAccount) GetAccountOpeningDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.AccountOpeningDatetime
	}
	return nil
}

type BankAccountHolder struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HolderType     AccountHolderTypeType  `protobuf:"varint,1,opt,name=holder_type,json=holderType,proto3,enum=wallet.AccountHolderTypeType" json:"holder_type,omitempty"`
	HolderName     string                 `protobuf:"bytes,2,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`             // Account Owner Name
	HolderNickname string                 `protobuf:"bytes,3,opt,name=holder_nickname,json=holderNickname,proto3" json:"holder_nickname,omitempty"` // Account Owner Nickname
	DocumentId     string                 `protobuf:"bytes,4,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BankAccountHolder) Reset() {
	*x = BankAccountHolder{}
	mi := &file_wallet_banking_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankAccountHolder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountHolder) ProtoMessage() {}

func (x *BankAccountHolder) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_banking_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountHolder.ProtoReflect.Descriptor instead.
func (*BankAccountHolder) Descriptor() ([]byte, []int) {
	return file_wallet_banking_proto_rawDescGZIP(), []int{1}
}

func (x *BankAccountHolder) GetHolderType() AccountHolderTypeType {
	if x != nil {
		return x.HolderType
	}
	return AccountHolderTypeType_NATURAL
}

func (x *BankAccountHolder) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *BankAccountHolder) GetHolderNickname() string {
	if x != nil {
		return x.HolderNickname
	}
	return ""
}

func (x *BankAccountHolder) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

var File_wallet_banking_proto protoreflect.FileDescriptor

const file_wallet_banking_proto_rawDesc = "" +
	"\n" +
	"\x14wallet/banking.proto\x12\x06wallet\x1a\x1fgoogle/protobuf/timestamp.proto\"\x93\x02\n" +
	"\vBankAccount\x12\x12\n" +
	"\x04ispb\x18\x01 \x01(\x05R\x04ispb\x12\x1f\n" +
	"\vbranch_code\x18\x02 \x01(\tR\n" +
	"branchCode\x12%\n" +
	"\x0eaccount_number\x18\x03 \x01(\tR\raccountNumber\x12:\n" +
	"\faccount_type\x18\x04 \x01(\x0e2\x17.wallet.BankAccountTypeR\vaccountType\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12T\n" +
	"\x18account_opening_datetime\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x16accountOpeningDatetime\"\xbe\x01\n" +
	"\x11BankAccountHolder\x12>\n" +
	"\vholder_type\x18\x01 \x01(\x0e2\x1d.wallet.AccountHolderTypeTypeR\n" +
	"holderType\x12\x1f\n" +
	"\vholder_name\x18\x02 \x01(\tR\n" +
	"holderName\x12'\n" +
	"\x0fholder_nickname\x18\x03 \x01(\tR\x0eholderNickname\x12\x1f\n" +
	"\vdocument_id\x18\x04 \x01(\tR\n" +
	"documentId*/\n" +
	"\x15AccountHolderTypeType\x12\v\n" +
	"\aNATURAL\x10\x00\x12\t\n" +
	"\x05LEGAL\x10\x01*e\n" +
	"\x0fBankAccountType\x12\x14\n" +
	"\x10CHECKING_ACCOUNT\x10\x00\x12\x12\n" +
	"\x0eSALARY_ACCOUNT\x10\x01\x12\x13\n" +
	"\x0fSAVINGS_ACCOUNT\x10\x02\x12\x13\n" +
	"\x0fPAYMENT_ACCOUNT\x10\x03B?Z=gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/walletb\x06proto3"

var (
	file_wallet_banking_proto_rawDescOnce sync.Once
	file_wallet_banking_proto_rawDescData []byte
)

func file_wallet_banking_proto_rawDescGZIP() []byte {
	file_wallet_banking_proto_rawDescOnce.Do(func() {
		file_wallet_banking_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_wallet_banking_proto_rawDesc), len(file_wallet_banking_proto_rawDesc)))
	})
	return file_wallet_banking_proto_rawDescData
}

var file_wallet_banking_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_wallet_banking_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_wallet_banking_proto_goTypes = []any{
	(AccountHolderTypeType)(0),    // 0: wallet.AccountHolderTypeType
	(BankAccountType)(0),          // 1: wallet.BankAccountType
	(*BankAccount)(nil),           // 2: wallet.BankAccount
	(*BankAccountHolder)(nil),     // 3: wallet.BankAccountHolder
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_wallet_banking_proto_depIdxs = []int32{
	1, // 0: wallet.BankAccount.account_type:type_name -> wallet.BankAccountType
	4, // 1: wallet.BankAccount.account_opening_datetime:type_name -> google.protobuf.Timestamp
	0, // 2: wallet.BankAccountHolder.holder_type:type_name -> wallet.AccountHolderTypeType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_wallet_banking_proto_init() }
func file_wallet_banking_proto_init() {
	if File_wallet_banking_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_wallet_banking_proto_rawDesc), len(file_wallet_banking_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wallet_banking_proto_goTypes,
		DependencyIndexes: file_wallet_banking_proto_depIdxs,
		EnumInfos:         file_wallet_banking_proto_enumTypes,
		MessageInfos:      file_wallet_banking_proto_msgTypes,
	}.Build()
	File_wallet_banking_proto = out.File
	file_wallet_banking_proto_goTypes = nil
	file_wallet_banking_proto_depIdxs = nil
}
