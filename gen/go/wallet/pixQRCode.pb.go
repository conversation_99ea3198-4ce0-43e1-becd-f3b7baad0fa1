// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: wallet/pixTransaction.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CommonResult represents a generic operation outcome.
// It is typically used in RPC API responses to signal success
//   - when no specific error message or detailed failure information is provided.
type QRCodeResponseType int32

const (
	QRCodeResponseType_QRCODE_RESPONSE_TYPE_IMAGE_ONLY   QRCodeResponseType = 0
	QRCodeResponseType_QRCODE_RESPONSE_TYPE_PAYLOAD_ONLY QRCodeResponseType = 1
	QRCodeResponseType_QRCODE_RESPONSE_TYPE_BOTH         QRCodeResponseType = 3
)

// Enum value maps for QRCodeResponseType.
var (
	QRCodeResponseType_name = map[int32]string{
		0: "QRCODE_RESPONSE_TYPE_IMAGE_ONLY",
		1: "QRCODE_RESPONSE_TYPE_PAYLOAD_ONLY",
		3: "QRCODE_RESPONSE_TYPE_BOTH",
	}
	QRCodeResponseType_value = map[string]int32{
		"QRCODE_RESPONSE_TYPE_IMAGE_ONLY":   0,
		"QRCODE_RESPONSE_TYPE_PAYLOAD_ONLY": 1,
		"QRCODE_RESPONSE_TYPE_BOTH":         3,
	}
)

func (x QRCodeResponseType) Enum() *QRCodeResponseType {
	p := new(QRCodeResponseType)
	*p = x
	return p
}

func (x QRCodeResponseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QRCodeResponseType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixQRCode_proto_enumTypes[0].Descriptor()
}

func (QRCodeResponseType) Type() protoreflect.EnumType {
	return &file_wallet_pixQRCode_proto_enumTypes[0]
}

func (x QRCodeResponseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QRCodeResponseType.Descriptor instead.
func (QRCodeResponseType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixQRCode_proto_rawDescGZIP(), []int{0}
}

type QRCodeType int32

const (
	QRCodeType_QRCODE_STATIC                   QRCodeType = 0
	QRCodeType_QRCODE_DYNAMIC_INSTANT_PAYMENT  QRCodeType = 1
	QRCodeType_QRCODE_DYNAMIC_EXPIRE_PAYMENT   QRCodeType = 2
	QRCodeType_QRCODE_DYNAMIC_COMBINED_PAYMENT QRCodeType = 3
)

// Enum value maps for QRCodeType.
var (
	QRCodeType_name = map[int32]string{
		0: "QRCODE_STATIC",
		1: "QRCODE_DYNAMIC_INSTANT_PAYMENT",
		2: "QRCODE_DYNAMIC_EXPIRE_PAYMENT",
		3: "QRCODE_DYNAMIC_COMBINED_PAYMENT",
	}
	QRCodeType_value = map[string]int32{
		"QRCODE_STATIC":                   0,
		"QRCODE_DYNAMIC_INSTANT_PAYMENT":  1,
		"QRCODE_DYNAMIC_EXPIRE_PAYMENT":   2,
		"QRCODE_DYNAMIC_COMBINED_PAYMENT": 3,
	}
)

func (x QRCodeType) Enum() *QRCodeType {
	p := new(QRCodeType)
	*p = x
	return p
}

func (x QRCodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QRCodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_wallet_pixQRCode_proto_enumTypes[1].Descriptor()
}

func (QRCodeType) Type() protoreflect.EnumType {
	return &file_wallet_pixQRCode_proto_enumTypes[1]
}

func (x QRCodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QRCodeType.Descriptor instead.
func (QRCodeType) EnumDescriptor() ([]byte, []int) {
	return file_wallet_pixQRCode_proto_rawDescGZIP(), []int{1}
}

var File_wallet_pixQRCode_proto protoreflect.FileDescriptor

const file_wallet_pixQRCode_proto_rawDesc = "" +
	"\n" +
	"\x16wallet/pixTransaction.proto\x12\x06wallet*\x7f\n" +
	"\x12QRCodeResponseType\x12#\n" +
	"\x1fQRCODE_RESPONSE_TYPE_IMAGE_ONLY\x10\x00\x12%\n" +
	"!QRCODE_RESPONSE_TYPE_PAYLOAD_ONLY\x10\x01\x12\x1d\n" +
	"\x19QRCODE_RESPONSE_TYPE_BOTH\x10\x03*\x8b\x01\n" +
	"\n" +
	"QRCodeType\x12\x11\n" +
	"\rQRCODE_STATIC\x10\x00\x12\"\n" +
	"\x1eQRCODE_DYNAMIC_INSTANT_PAYMENT\x10\x01\x12!\n" +
	"\x1dQRCODE_DYNAMIC_EXPIRE_PAYMENT\x10\x02\x12#\n" +
	"\x1fQRCODE_DYNAMIC_COMBINED_PAYMENT\x10\x03B?Z=gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/walletb\x06proto3"

var (
	file_wallet_pixQRCode_proto_rawDescOnce sync.Once
	file_wallet_pixQRCode_proto_rawDescData []byte
)

func file_wallet_pixQRCode_proto_rawDescGZIP() []byte {
	file_wallet_pixQRCode_proto_rawDescOnce.Do(func() {
		file_wallet_pixQRCode_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_wallet_pixQRCode_proto_rawDesc), len(file_wallet_pixQRCode_proto_rawDesc)))
	})
	return file_wallet_pixQRCode_proto_rawDescData
}

var file_wallet_pixQRCode_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_wallet_pixQRCode_proto_goTypes = []any{
	(QRCodeResponseType)(0), // 0: wallet.QRCodeResponseType
	(QRCodeType)(0),         // 1: wallet.QRCodeType
}
var file_wallet_pixQRCode_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_wallet_pixQRCode_proto_init() }
func file_wallet_pixQRCode_proto_init() {
	if File_wallet_pixQRCode_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_wallet_pixQRCode_proto_rawDesc), len(file_wallet_pixQRCode_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wallet_pixQRCode_proto_goTypes,
		DependencyIndexes: file_wallet_pixQRCode_proto_depIdxs,
		EnumInfos:         file_wallet_pixQRCode_proto_enumTypes,
	}.Build()
	File_wallet_pixQRCode_proto = out.File
	file_wallet_pixQRCode_proto_goTypes = nil
	file_wallet_pixQRCode_proto_depIdxs = nil
}